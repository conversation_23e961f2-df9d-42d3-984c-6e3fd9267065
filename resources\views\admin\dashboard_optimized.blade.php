@extends('layouts.admin_optimized')

@section('title', 'Tableau de bord administrateur')

@section('content')
<div class="container-fluid py-4">
    <!-- En-tête de bienvenue simplifié -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="h3 mb-1">Bonjour, {{ Auth::user()->name }} 👋</h1>
                            <p class="text-muted mb-0">Tableau de bord administrateur - {{ now()->format('d M Y, H:i') }}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex gap-2 justify-content-end">
                                <span class="badge bg-success">Système opérationnel</span>
                                <span class="badge bg-primary">{{ $stats['total_orders'] ?? 0 }} commandes</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales - Simplifiées -->
    <div class="row g-4 mb-4">
        <!-- Revenus totaux -->
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="fas fa-chart-line text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-1">{{ number_format($stats['monthly_revenue'] ?? 0) }}</h3>
                            <p class="text-muted mb-0">Revenus mensuels (FCFA)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commandes totales -->
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="fas fa-shopping-cart text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-1">{{ $stats['total_orders'] ?? 0 }}</h3>
                            <p class="text-muted mb-0">Total commandes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Utilisateurs -->
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="fas fa-users text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-1">{{ $stats['total_users'] ?? 0 }}</h3>
                            <p class="text-muted mb-0">Utilisateurs actifs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Produits -->
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="fas fa-box text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-1">{{ $stats['total_products'] ?? 0 }}</h3>
                            <p class="text-muted mb-0">Produits disponibles</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques simplifiés -->
    <div class="row g-4 mb-4">
        <!-- Graphique des revenus -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Évolution des revenus</h5>
                </div>
                <div class="card-body">
                    <div id="revenueChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- Répartition par catégorie -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Répartition par catégorie</h5>
                </div>
                <div class="card-body">
                    <div id="categoryChart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableaux de données -->
    <div class="row g-4">
        <!-- Commandes récentes -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Commandes récentes</h5>
                </div>
                <div class="card-body">
                    @if(isset($pendingSupplies) && $pendingSupplies->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Client</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($pendingSupplies->take(5) as $supply)
                                    <tr>
                                        <td>#{{ $supply->id }}</td>
                                        <td>{{ $supply->customer_name ?? 'N/A' }}</td>
                                        <td>{{ number_format($supply->total_amount ?? 0) }} FCFA</td>
                                        <td>
                                            <span class="badge bg-warning">{{ $supply->status ?? 'En attente' }}</span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">Aucune commande récente</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Utilisateurs récents -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Utilisateurs récents</h5>
                </div>
                <div class="card-body">
                    @if(isset($latest_users) && $latest_users->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Email</th>
                                        <th>Rôle</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($latest_users->take(5) as $user)
                                    <tr>
                                        <td>{{ $user->name }}</td>
                                        <td>{{ $user->email }}</td>
                                        <td>
                                            <span class="badge bg-primary">{{ $user->getRoleNames()->first() ?? 'User' }}</span>
                                        </td>
                                        <td>{{ $user->created_at->format('d/m/Y') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-muted">Aucun utilisateur récent</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Chart.js - Plus léger qu'ApexCharts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration simplifiée des graphiques
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0,0,0,0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    };

    // Données simplifiées
    const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'];
    const revenueData = [15000, 25000, 20000, 30000, 28000, 35000];
    const categoryData = [45, 30, 15, 10];
    const categoryLabels = ['Ciment', 'Fer', 'Sable', 'Gravier'];

    // Graphique des revenus
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Revenus',
                    data: revenueData,
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: chartOptions
        });
    }

    // Graphique des catégories
    const categoryCtx = document.getElementById('categoryChart');
    if (categoryCtx) {
        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: categoryLabels,
                datasets: [{
                    data: categoryData,
                    backgroundColor: [
                        '#2563eb',
                        '#16a34a',
                        '#ca8a04',
                        '#dc2626'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    console.log('✅ Dashboard admin optimisé chargé avec succès');
});
</script>
@endpush
