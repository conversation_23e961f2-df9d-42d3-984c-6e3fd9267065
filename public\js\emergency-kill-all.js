/**
 * GRADIS - ARRÊT D'URGENCE TOTAL
 * Stoppe IMMÉDIATEMENT tous les processus qui tournent en boucle
 * Version: URGENCE - Arrêt brutal de tout
 */

console.log('🚨 ARRÊT D\'URGENCE TOTAL EN COURS...');

/**
 * ARRÊT BRUTAL DE TOUS LES PROCESSUS
 */
(function() {
    'use strict';
    
    console.log('🛑 DÉBUT ARRÊT D\'URGENCE TOTAL');
    
    // 1. STOPPER TOUS LES TIMERS EXISTANTS (méthode brutale)
    console.log('⏹️ Arrêt de tous les timers...');
    
    // Stopper tous les setTimeout
    let maxTimeout = 0;
    const testTimeout = setTimeout(() => {}, 1);
    maxTimeout = testTimeout;
    clearTimeout(testTimeout);
    
    for (let i = 1; i <= maxTimeout + 1000; i++) {
        try {
            clearTimeout(i);
        } catch (e) {
            // Ignorer les erreurs
        }
    }
    
    // Stopper tous les setInterval
    let maxInterval = 0;
    const testInterval = setInterval(() => {}, 1);
    maxInterval = testInterval;
    clearInterval(testInterval);
    
    for (let i = 1; i <= maxInterval + 1000; i++) {
        try {
            clearInterval(i);
        } catch (e) {
            // Ignorer les erreurs
        }
    }
    
    console.log('✅ Tous les timers stoppés');
    
    // 2. BLOQUER DÉFINITIVEMENT setTimeout ET setInterval
    console.log('🚫 Blocage définitif des nouveaux timers...');
    
    window.setTimeout = function(callback, delay) {
        console.warn('🚫 setTimeout BLOQUÉ par arrêt d\'urgence');
        return null;
    };
    
    window.setInterval = function(callback, delay) {
        console.warn('🚫 setInterval BLOQUÉ par arrêt d\'urgence');
        return null;
    };
    
    // 3. STOPPER LES REQUÊTES EN COURS
    console.log('🌐 Arrêt des requêtes en cours...');
    
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            console.warn('🚫 Requête BLOQUÉE par arrêt d\'urgence:', url);
            return Promise.reject(new Error('Arrêt d\'urgence - Requête bloquée'));
        };
    }
    
    // 4. STOPPER LES ANIMATIONS CSS
    console.log('🎨 Arrêt des animations CSS...');
    
    const style = document.createElement('style');
    style.textContent = `
        *, *::before, *::after {
            animation-duration: 0s !important;
            animation-delay: 0s !important;
            transition-duration: 0s !important;
            transition-delay: 0s !important;
        }
    `;
    document.head.appendChild(style);
    
    // 5. NETTOYER LES EVENT LISTENERS PROBLÉMATIQUES
    console.log('👂 Nettoyage des event listeners...');
    
    // Supprimer tous les event listeners sur window
    const newWindow = window.constructor.prototype;
    for (let prop in newWindow) {
        if (prop.startsWith('on')) {
            try {
                window[prop] = null;
            } catch (e) {
                // Ignorer les erreurs
            }
        }
    }
    
    // 6. BLOQUER LES GÉNÉRATEURS JAVASCRIPT (Generator.next)
    console.log('⚙️ Blocage des générateurs...');
    
    if (window.Generator && window.Generator.prototype.next) {
        window.Generator.prototype.next = function() {
            console.warn('🚫 Generator.next BLOQUÉ par arrêt d\'urgence');
            return { value: undefined, done: true };
        };
    }
    
    // 7. CRÉER UN INDICATEUR VISUEL D'ARRÊT D'URGENCE
    console.log('🚨 Création indicateur visuel...');
    
    const emergencyIndicator = document.createElement('div');
    emergencyIndicator.id = 'emergency-stop-indicator';
    emergencyIndicator.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 999999;
        background: linear-gradient(90deg, #dc3545, #ff6b6b);
        color: white;
        text-align: center;
        padding: 10px;
        font-weight: bold;
        font-family: Arial, sans-serif;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        border-bottom: 3px solid #c82333;
    `;
    
    emergencyIndicator.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
            <span style="font-size: 18px;">🛑</span>
            <span>ARRÊT D'URGENCE ACTIVÉ - Tous les processus en boucle ont été stoppés</span>
            <button onclick="location.reload()" style="
                background: rgba(255,255,255,0.2); 
                border: 1px solid rgba(255,255,255,0.3); 
                color: white; 
                padding: 5px 10px; 
                border-radius: 4px; 
                cursor: pointer;
                font-size: 12px;
            ">🔄 Recharger la page</button>
        </div>
    `;
    
    document.body.appendChild(emergencyIndicator);
    
    // 8. SURVEILLER ET BLOQUER LES NOUVEAUX PROCESSUS
    console.log('👁️ Surveillance active...');
    
    // Surveiller les nouvelles tentatives de création de timers
    let blockedAttempts = 0;
    
    const originalConsoleWarn = console.warn;
    console.warn = function(...args) {
        if (args[0] && args[0].includes('BLOQUÉ par arrêt d\'urgence')) {
            blockedAttempts++;
            if (blockedAttempts % 10 === 0) {
                console.log(`🚫 ${blockedAttempts} tentatives de processus bloquées`);
            }
        }
        return originalConsoleWarn.apply(this, args);
    };
    
    // 9. NETTOYER LES SCRIPTS PROBLÉMATIQUES SPÉCIFIQUES
    console.log('🧹 Nettoyage scripts spécifiques...');
    
    // Bloquer cleanup-old-scripts.js
    if (window.cleanupOldScripts) {
        window.cleanupOldScripts = function() {
            console.warn('🚫 cleanupOldScripts BLOQUÉ');
        };
    }
    
    // Bloquer admin-stable.js
    if (window.AdminStable) {
        window.AdminStable = null;
    }
    
    // Bloquer les processus de surveillance
    if (window.startPerformanceMonitoring) {
        window.startPerformanceMonitoring = function() {
            console.warn('🚫 Performance monitoring BLOQUÉ');
        };
    }
    
    // 10. MESSAGE FINAL
    console.log('✅ ARRÊT D\'URGENCE TERMINÉ');
    console.log('📊 Résultats:');
    console.log('   - Tous les setTimeout/setInterval stoppés');
    console.log('   - Nouveaux timers bloqués définitivement');
    console.log('   - Requêtes fetch bloquées');
    console.log('   - Animations CSS désactivées');
    console.log('   - Générateurs JavaScript bloqués');
    console.log('   - Scripts problématiques neutralisés');
    
    // Afficher un message dans la page
    setTimeout(() => {
        if (document.body) {
            const message = document.createElement('div');
            message.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 15px;
                border-radius: 5px;
                font-weight: bold;
                z-index: 999998;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            `;
            message.innerHTML = `
                ✅ ARRÊT D'URGENCE RÉUSSI<br>
                <small>Les boucles infinies ont été stoppées</small>
            `;
            document.body.appendChild(message);
            
            // Masquer après 5 secondes
            setTimeout(() => {
                if (message.parentNode) {
                    message.remove();
                }
            }, 5000);
        }
    }, 100);
    
})();

console.log('🎯 Script d\'arrêt d\'urgence chargé et prêt');
