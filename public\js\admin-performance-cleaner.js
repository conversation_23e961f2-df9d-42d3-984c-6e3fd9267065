/**
 * GRADIS Admin Performance Cleaner
 * Script pour nettoyer les performances des vues admin
 * Supprime les auto-refresh, timeouts longs, et logs excessifs
 */

console.log('🧹 GRADIS Admin Performance Cleaner - Nettoyage en cours...');

// Configuration de nettoyage
const PERFORMANCE_CONFIG = {
    // Supprimer tous les console.log en production
    REMOVE_CONSOLE_LOGS: true,
    
    // Supprimer les auto-refresh
    DISABLE_AUTO_REFRESH: true,
    
    // Supprimer les setTimeout longs (> 1000ms)
    DISABLE_LONG_TIMEOUTS: true,
    
    // Supprimer les setInterval
    DISABLE_INTERVALS: true,
    
    // Optimiser les animations
    OPTIMIZE_ANIMATIONS: true
};

/**
 * Nettoyer les console.log
 */
function cleanConsoleLogs() {
    if (PERFORMANCE_CONFIG.REMOVE_CONSOLE_LOGS) {
        // Remplacer console.log par une fonction vide
        const originalConsole = window.console;
        window.console = {
            ...originalConsole,
            log: function() {}, // Supprimer les logs
            info: function() {}, // Supprimer les infos
            warn: originalConsole.warn, // Garder les warnings
            error: originalConsole.error // Garder les erreurs
        };
        
        console.warn('✅ Console logs nettoyés pour optimiser les performances');
    }
}

/**
 * Supprimer les auto-refresh
 */
function disableAutoRefresh() {
    if (PERFORMANCE_CONFIG.DISABLE_AUTO_REFRESH) {
        // Intercepter les location.reload()
        const originalReload = window.location.reload;
        window.location.reload = function() {
            console.warn('🚫 Auto-refresh bloqué pour optimiser les performances');
            return false;
        };
        
        // Supprimer les meta refresh
        const metaRefresh = document.querySelector('meta[http-equiv="refresh"]');
        if (metaRefresh) {
            metaRefresh.remove();
            console.warn('✅ Meta refresh supprimé');
        }
        
        console.warn('✅ Auto-refresh désactivé');
    }
}

/**
 * Nettoyer les setTimeout longs
 */
function cleanLongTimeouts() {
    if (PERFORMANCE_CONFIG.DISABLE_LONG_TIMEOUTS) {
        const originalSetTimeout = window.setTimeout;
        
        window.setTimeout = function(callback, delay, ...args) {
            // Bloquer les timeouts > 1000ms
            if (delay > 1000) {
                console.warn(`🚫 Timeout long bloqué: ${delay}ms`);
                return null;
            }
            
            return originalSetTimeout.call(this, callback, delay, ...args);
        };
        
        console.warn('✅ Timeouts longs nettoyés');
    }
}

/**
 * Supprimer tous les setInterval
 */
function disableIntervals() {
    if (PERFORMANCE_CONFIG.DISABLE_INTERVALS) {
        const originalSetInterval = window.setInterval;
        
        window.setInterval = function(callback, delay, ...args) {
            console.warn(`🚫 Interval bloqué: ${delay}ms`);
            return null;
        };
        
        // Nettoyer les intervals existants
        let intervalId = setInterval(() => {}, 1);
        for (let i = 1; i <= intervalId; i++) {
            clearInterval(i);
        }
        
        console.warn('✅ Tous les intervals supprimés');
    }
}

/**
 * Optimiser les animations CSS
 */
function optimizeAnimations() {
    if (PERFORMANCE_CONFIG.OPTIMIZE_ANIMATIONS) {
        // Ajouter du CSS pour désactiver les animations lourdes
        const style = document.createElement('style');
        style.textContent = `
            /* Optimisations des animations */
            *, *::before, *::after {
                animation-duration: 0.1s !important;
                animation-delay: 0s !important;
                transition-duration: 0.1s !important;
                transition-delay: 0s !important;
            }
            
            /* Désactiver les animations de transformation lourdes */
            .bg-shape, .welcome-bg-animated, .icon-glow, .avatar-ring {
                animation: none !important;
                transform: none !important;
            }
            
            /* Optimiser les gradients */
            .modern-welcome-card, .top-navbar {
                background: var(--primary-color) !important;
            }
        `;
        
        document.head.appendChild(style);
        console.warn('✅ Animations optimisées');
    }
}

/**
 * Nettoyer les scripts ApexCharts lourds
 */
function cleanHeavyScripts() {
    // Supprimer les scripts ApexCharts si présents
    const apexScripts = document.querySelectorAll('script[src*="apexcharts"]');
    apexScripts.forEach(script => {
        script.remove();
        console.warn('✅ Script ApexCharts supprimé');
    });
    
    // Bloquer le chargement d'ApexCharts
    if (window.ApexCharts) {
        window.ApexCharts = null;
        console.warn('✅ ApexCharts désactivé');
    }
}

/**
 * Optimiser les requêtes AJAX
 */
function optimizeAjaxRequests() {
    // Intercepter les requêtes fetch fréquentes
    const originalFetch = window.fetch;
    const requestCache = new Map();
    
    window.fetch = function(url, options = {}) {
        // Cache simple pour éviter les requêtes répétées
        const cacheKey = url + JSON.stringify(options);
        const cached = requestCache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < 30000) { // Cache 30s
            console.warn('📦 Requête mise en cache:', url);
            return Promise.resolve(cached.response.clone());
        }
        
        return originalFetch.call(this, url, options).then(response => {
            if (response.ok) {
                requestCache.set(cacheKey, {
                    response: response.clone(),
                    timestamp: Date.now()
                });
            }
            return response;
        });
    };
    
    console.warn('✅ Requêtes AJAX optimisées');
}

/**
 * Initialisation du nettoyage
 */
function initPerformanceCleaner() {
    console.warn('🚀 Initialisation du nettoyage des performances...');
    
    // Nettoyer immédiatement
    cleanConsoleLogs();
    disableAutoRefresh();
    cleanLongTimeouts();
    disableIntervals();
    optimizeAnimations();
    cleanHeavyScripts();
    optimizeAjaxRequests();
    
    console.warn('🎉 Nettoyage des performances terminé!');
    console.warn('📊 Performances admin optimisées comme cement-manager');
}

/**
 * Créer un bouton de contrôle des performances
 */
function createPerformanceControls() {
    const controlPanel = document.createElement('div');
    controlPanel.id = 'performance-controls';
    controlPanel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: white;
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        font-size: 12px;
    `;
    
    controlPanel.innerHTML = `
        <div style="margin-bottom: 5px;">
            <strong>🚀 Performance Admin</strong>
        </div>
        <button onclick="location.reload()" style="padding: 5px 10px; margin-right: 5px; border: none; background: #dc3545; color: white; border-radius: 4px; cursor: pointer;">
            Recharger
        </button>
        <button onclick="window.console.warn('📊 Performances optimisées!')" style="padding: 5px 10px; border: none; background: #28a745; color: white; border-radius: 4px; cursor: pointer;">
            Status
        </button>
    `;
    
    document.body.appendChild(controlPanel);
}

// Auto-initialisation
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        initPerformanceCleaner();
        createPerformanceControls();
    });
} else {
    initPerformanceCleaner();
    createPerformanceControls();
}

// Export global
window.AdminPerformanceCleaner = {
    init: initPerformanceCleaner,
    clean: cleanConsoleLogs,
    optimize: optimizeAnimations
};
