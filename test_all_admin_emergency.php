<?php

/**
 * Test Final - Vérification arrêt d'urgence sur TOUTES les vues admin
 * Confirmer que toutes les vues admin sont protégées contre les boucles
 */

echo "🎯 TEST FINAL - Arrêt d'Urgence sur TOUTES les Vues Admin\n";
echo "=========================================================\n\n";

// Fonction pour scanner toutes les vues admin
function getAllAdminViews($directory, &$files) {
    if (!is_dir($directory)) return;
    
    $items = scandir($directory);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $path = $directory . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($path)) {
            getAllAdminViews($path, $files);
        } elseif (pathinfo($item, PATHINFO_EXTENSION) === 'php' && 
                  strpos($item, '.blade.php') !== false) {
            $files[] = $path;
        }
    }
}

// Scanner toutes les vues admin
$adminViews = [];
getAllAdminViews('resources/views/admin', $adminViews);

echo "🔍 Total vues admin trouvées: " . count($adminViews) . "\n\n";

$protectedViews = 0;
$unprotectedViews = 0;
$specialCases = 0;

echo "📊 ANALYSE DE PROTECTION:\n";
echo "========================\n";

foreach ($adminViews as $viewFile) {
    $relativePath = str_replace(['resources/views/', '\\'], ['', '/'], $viewFile);
    $viewName = basename($viewFile, '.blade.php');
    
    if (!file_exists($viewFile)) continue;
    
    $content = file_get_contents($viewFile);
    
    // Vérifier le layout utilisé
    if (strpos($content, "@extends('layouts.admin_minimal')") !== false) {
        echo "✅ $relativePath → PROTÉGÉ (admin_minimal)\n";
        $protectedViews++;
    } elseif (strpos($content, "@extends('layouts.admin')") !== false) {
        echo "❌ $relativePath → NON PROTÉGÉ (admin standard)\n";
        $unprotectedViews++;
    } elseif (strpos($content, "@extends") !== false) {
        // Autre layout
        preg_match("/@extends\(['\"]([^'\"]+)['\"]\)/", $content, $matches);
        $layout = isset($matches[1]) ? $matches[1] : 'inconnu';
        echo "⚠️ $relativePath → LAYOUT SPÉCIAL ($layout)\n";
        $specialCases++;
    } else {
        echo "❓ $relativePath → PAS DE LAYOUT DÉTECTÉ\n";
        $specialCases++;
    }
}

echo "\n📊 RÉSUMÉ DE PROTECTION:\n";
echo "=======================\n";
echo "✅ Vues protégées (admin_minimal): $protectedViews\n";
echo "❌ Vues non protégées (admin): $unprotectedViews\n";
echo "⚠️ Cas spéciaux: $specialCases\n";
echo "📁 Total: " . count($adminViews) . "\n\n";

// Calculer le pourcentage de protection
$totalRelevant = $protectedViews + $unprotectedViews;
if ($totalRelevant > 0) {
    $protectionRate = round(($protectedViews / $totalRelevant) * 100, 1);
    echo "📈 Taux de protection: $protectionRate%\n\n";
}

// Vérifier le layout admin_minimal
echo "🔧 VÉRIFICATION LAYOUT ADMIN_MINIMAL:\n";
echo "====================================\n";

$minimalLayoutPath = 'resources/views/layouts/admin_minimal.blade.php';
if (file_exists($minimalLayoutPath)) {
    $layoutContent = file_get_contents($minimalLayoutPath);
    
    // Vérifications essentielles
    $checks = [
        'asset(\'js/emergency-kill-all.js\')' => 'Script d\'arrêt d\'urgence',
        'asset(\'js/jquery-3.7.1.min.js\')' => 'jQuery local',
        '<!-- cleanup-old-scripts.js' => 'Scripts problématiques supprimés',
        'bootstrap@5.3.2' => 'Bootstrap',
        'sweetalert2@11' => 'SweetAlert2'
    ];
    
    foreach ($checks as $feature => $searchTerm) {
        if (strpos($layoutContent, $searchTerm) !== false) {
            echo "✅ $feature présent\n";
        } else {
            echo "❌ $feature manquant\n";
        }
    }
} else {
    echo "❌ Layout admin_minimal non trouvé\n";
}

echo "\n";

// Vérifier le script d'arrêt d'urgence
echo "🛑 VÉRIFICATION SCRIPT D'ARRÊT D'URGENCE:\n";
echo "========================================\n";

$emergencyScriptPath = 'public/js/emergency-kill-all.js';
if (file_exists($emergencyScriptPath)) {
    echo "✅ Script emergency-kill-all.js présent\n";
    
    $scriptContent = file_get_contents($emergencyScriptPath);
    $scriptFeatures = [
        'clearTimeout' => 'Arrêt des setTimeout',
        'clearInterval' => 'Arrêt des setInterval',
        'Generator.prototype.next' => 'Blocage des générateurs',
        'emergency-stop-indicator' => 'Indicateur visuel',
        'ARRÊT D\'URGENCE TOTAL' => 'Message d\'arrêt'
    ];
    
    foreach ($scriptFeatures as $feature => $description) {
        if (strpos($scriptContent, $feature) !== false) {
            echo "✅ $description configuré\n";
        } else {
            echo "❌ $description manquant\n";
        }
    }
} else {
    echo "❌ Script d'arrêt d'urgence manquant\n";
}

echo "\n";

// Instructions de test
echo "🧪 INSTRUCTIONS DE TEST:\n";
echo "=======================\n";

if ($protectionRate >= 90) {
    echo "🎉 EXCELLENT! Plus de 90% des vues sont protégées\n\n";
    
    echo "✅ Testez maintenant TOUTES ces vues admin:\n";
    echo "   - /admin/dashboard\n";
    echo "   - /admin/users\n";
    echo "   - /admin/products\n";
    echo "   - /admin/categories\n";
    echo "   - /admin/supplies\n";
    echo "   - /admin/drivers\n";
    echo "   - /admin/sales\n";
    echo "   - /admin/reports\n";
    echo "   - Toutes les autres vues admin\n\n";
    
    echo "🔍 Sur CHAQUE vue, vous devriez voir:\n";
    echo "   1. Barre rouge 'ARRÊT D'URGENCE ACTIVÉ' en haut\n";
    echo "   2. Chargement en < 5 secondes (vs 40+ avant)\n";
    echo "   3. Console propre sans erreurs de boucles\n";
    echo "   4. Boutons JavaScript fonctionnels\n";
    echo "   5. Plus d'erreurs ERR_CONNECTION_RESET\n\n";
    
    echo "🎯 RÉSULTATS ATTENDUS:\n";
    echo "   - TOUTES les vues admin chargent rapidement\n";
    echo "   - Plus de boucles infinies nulle part\n";
    echo "   - Interface réactive sur toutes les pages\n";
    echo "   - Console JavaScript propre\n\n";
    
    echo "🎉 SUCCÈS TOTAL!\n";
    echo "L'arrêt d'urgence est maintenant appliqué à TOUTES les vues admin!\n";
    echo "Les 40 secondes de chargement sont éliminées partout!\n";
    
} elseif ($unprotectedViews > 0) {
    echo "⚠️ ATTENTION! $unprotectedViews vues ne sont pas encore protégées\n\n";
    
    echo "🔧 Pour corriger les vues restantes:\n";
    echo "   1. Relancez: php fix_all_admin_views.php\n";
    echo "   2. Ou corrigez manuellement les vues non protégées\n";
    echo "   3. Changez @extends('layouts.admin') → @extends('layouts.admin_minimal')\n\n";
    
} else {
    echo "✅ Toutes les vues principales sont protégées\n";
    echo "⚠️ Vérifiez les cas spéciaux si nécessaire\n\n";
}

echo "✨ Test terminé!\n";
echo "Vérification complète de la protection contre les boucles infinies effectuée!\n";
