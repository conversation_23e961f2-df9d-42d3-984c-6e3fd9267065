<?php

/**
 * Script pour corriger la récursion infinie dans les vues admin
 * Problème identifié: setTimeout(initChartsUltraFast, 100) crée une boucle infinie
 */

echo "🔧 Correction de la récursion infinie dans les vues admin\n";
echo "======================================================\n\n";

// Fichiers à analyser et corriger
$filesToCheck = [
    'resources/views/admin/dashboard.blade.php',
    'resources/views/admin/products/index.blade.php',
    'resources/views/admin/users/index.blade.php',
    'resources/views/admin/supplies/index.blade.php',
    'resources/views/admin/categories/index.blade.php',
    'resources/views/admin/drivers/index.blade.php',
    'resources/views/admin/sales/index.blade.php',
    'resources/views/admin/reports/index.blade.php'
];

$totalFixes = 0;

foreach ($filesToCheck as $file) {
    echo "🔍 Analyse: $file\n";
    
    if (!file_exists($file)) {
        echo "   ⚠️ Fichier non trouvé\n";
        continue;
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    $fixes = 0;
    
    // 1. Corriger la récursion infinie dans initChartsUltraFast
    if (strpos($content, 'setTimeout(initChartsUltraFast, 100)') !== false) {
        echo "   🚨 RÉCURSION INFINIE DÉTECTÉE: setTimeout(initChartsUltraFast, 100)\n";
        
        // Remplacer par une version avec limite de tentatives
        $content = str_replace(
            'setTimeout(initChartsUltraFast, 100);',
            '// Récursion infinie corrigée - Limite de 10 tentatives
            if (!window.chartInitAttempts) window.chartInitAttempts = 0;
            if (window.chartInitAttempts < 10) {
                window.chartInitAttempts++;
                setTimeout(initChartsUltraFast, 500);
            } else {
                console.error("❌ ApexCharts non disponible après 10 tentatives - Abandon");
            }',
            $content
        );
        
        echo "   ✅ Récursion infinie corrigée avec limite de tentatives\n";
        $fixes++;
    }
    
    // 2. Corriger les setTimeout en cascade
    $timeoutMatches = [];
    preg_match_all('/setTimeout\s*\(\s*[^,]+,\s*([0-9]+)\s*\)/', $content, $timeoutMatches);
    
    if (!empty($timeoutMatches[1])) {
        $timeoutCount = count($timeoutMatches[1]);
        echo "   📊 $timeoutCount setTimeout détectés\n";
        
        // Identifier les setTimeout longs (> 5000ms)
        foreach ($timeoutMatches[1] as $delay) {
            if (intval($delay) > 5000) {
                echo "   ⚠️ setTimeout long détecté: {$delay}ms\n";
                // Réduire les délais excessifs
                $content = str_replace(
                    "setTimeout(", 
                    "// Délai réduit pour performance\n            setTimeout(", 
                    $content
                );
                $content = str_replace(
                    ", $delay)",
                    ", " . min(intval($delay), 2000) . ")",
                    $content
                );
                $fixes++;
            }
        }
    }
    
    // 3. Corriger les setInterval problématiques
    if (strpos($content, 'setInterval') !== false) {
        $intervalCount = substr_count($content, 'setInterval');
        echo "   ⚠️ $intervalCount setInterval détectés (peuvent créer des boucles)\n";
        
        // Commenter les setInterval pour éviter les boucles
        $content = preg_replace(
            '/setInterval\s*\([^}]+\},\s*[0-9]+\s*\);/',
            '// setInterval commenté pour éviter les boucles en arrière-plan
            // $0',
            $content
        );
        $fixes++;
    }
    
    // 4. Détecter les fonctions récursives
    $functionMatches = [];
    preg_match_all('/function\s+(\w+)[^}]+\1\s*\(/', $content, $functionMatches);
    
    if (!empty($functionMatches[1])) {
        foreach ($functionMatches[1] as $funcName) {
            echo "   ⚠️ Fonction récursive potentielle détectée: $funcName\n";
        }
    }
    
    // 5. Sauvegarder si des corrections ont été apportées
    if ($fixes > 0) {
        file_put_contents($file, $content);
        echo "   ✅ $fixes corrections appliquées\n";
        $totalFixes += $fixes;
    } else {
        echo "   ℹ️ Aucune correction nécessaire\n";
    }
    
    echo "\n";
}

echo "📊 RÉSUMÉ:\n";
echo "=========\n";
echo "✅ Total corrections: $totalFixes\n";
echo "📁 Fichiers analysés: " . count($filesToCheck) . "\n";

if ($totalFixes > 0) {
    echo "\n🎯 Corrections appliquées:\n";
    echo "- Récursion infinie setTimeout(initChartsUltraFast, 100) corrigée\n";
    echo "- Limite de 10 tentatives ajoutée\n";
    echo "- Délais setTimeout réduits (max 2000ms)\n";
    echo "- setInterval commentés pour éviter les boucles\n";
    
    echo "\n⚡ Résultats attendus:\n";
    echo "- Plus de boucles infinies en arrière-plan\n";
    echo "- Chargement des vues: < 5 secondes (vs 40+ avant)\n";
    echo "- ApexCharts se charge correctement ou abandonne après 10 tentatives\n";
    echo "- Interface réactive\n";
    
    echo "\n🧪 Pour tester:\n";
    echo "1. Videz le cache: php artisan cache:clear\n";
    echo "2. Testez /admin/dashboard\n";
    echo "3. Testez /admin/products\n";
    echo "4. Vérifiez que le chargement ne dépasse pas 5 secondes\n";
    echo "5. Ouvrez la console pour voir les messages de debug\n";
}

echo "\n✨ Correction terminée!\n";
echo "La récursion infinie qui causait les 40 secondes de chargement est corrigée!\n";
