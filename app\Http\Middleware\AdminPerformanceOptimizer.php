<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Config;

class AdminPerformanceOptimizer
{
    /**
     * Middleware pour optimiser les performances des vues admin
     * Applique automatiquement les optimisations basées sur la configuration
     */
    public function handle(Request $request, Closure $next)
    {
        // Vérifier si on est dans l'interface admin
        if (!$request->is('admin*')) {
            return $next($request);
        }

        // Appliquer les optimisations selon la configuration
        $this->applyPerformanceOptimizations();

        $response = $next($request);

        // Post-traitement de la réponse si nécessaire
        if ($response->headers->get('content-type') && 
            strpos($response->headers->get('content-type'), 'text/html') !== false) {
            
            $content = $response->getContent();
            $content = $this->optimizeHtmlContent($content);
            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Appliquer les optimisations de performance
     */
    private function applyPerformanceOptimizations()
    {
        $config = config('admin_performance');

        // Partager les variables de configuration avec les vues
        View::share('adminPerformanceConfig', $config);

        // Définir le layout à utiliser
        if ($config['optimized_mode']) {
            View::share('adminLayout', 'layouts.' . $config['layout']);
        }

        // Configurer les scripts
        View::share('useOptimizedScripts', $config['scripts']['use_chartjs']);
        View::share('disableHeavyAnimations', $config['scripts']['disable_heavy_animations']);
    }

    /**
     * Optimiser le contenu HTML
     */
    private function optimizeHtmlContent($content)
    {
        $config = config('admin_performance');

        // Supprimer les scripts ApexCharts si désactivés
        if ($config['scripts']['disable_apexcharts']) {
            $content = preg_replace('/<script[^>]*apexcharts[^>]*><\/script>/i', '', $content);
            $content = preg_replace('/<script[^>]*>.*?ApexCharts.*?<\/script>/is', '', $content);
        }

        // Supprimer les console.log si désactivés
        if ($config['logging']['disable_console_logs']) {
            $content = preg_replace('/console\.log\([^)]*\);?/i', '', $content);
            $content = preg_replace('/console\.info\([^)]*\);?/i', '', $content);
        }

        // Supprimer les setTimeout longs
        if ($config['timers']['disable_long_timeouts']) {
            $maxDelay = $config['timers']['max_timeout_delay'];
            $content = preg_replace_callback(
                '/setTimeout\([^,]+,\s*(\d+)\)/i',
                function ($matches) use ($maxDelay) {
                    $delay = intval($matches[1]);
                    if ($delay > $maxDelay) {
                        return "// setTimeout supprimé (délai trop long: {$delay}ms)";
                    }
                    return $matches[0];
                },
                $content
            );
        }

        // Supprimer les setInterval si désactivés
        if ($config['timers']['disable_intervals']) {
            $content = preg_replace('/setInterval\([^)]+\);?/i', '// setInterval supprimé', $content);
        }

        // Supprimer les location.reload() si auto-refresh désactivé
        if ($config['timers']['disable_auto_refresh']) {
            $content = preg_replace('/location\.reload\(\);?/i', '// Auto-refresh désactivé', $content);
        }

        // Minifier le HTML si activé
        if (config('app.env') === 'production') {
            $content = $this->minifyHtml($content);
        }

        return $content;
    }

    /**
     * Minifier le HTML
     */
    private function minifyHtml($content)
    {
        // Supprimer les commentaires HTML (sauf les commentaires conditionnels)
        $content = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $content);
        
        // Supprimer les espaces multiples
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Supprimer les espaces autour des balises
        $content = preg_replace('/>\s+</', '><', $content);
        
        return trim($content);
    }
}
