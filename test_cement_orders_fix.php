<?php

/**
 * Test pour vérifier que les références aux cement-orders ont été supprimées
 */

echo "🔧 Test Suppression Cement Orders - Dashboard Optimisé\n";
echo "=====================================================\n\n";

// Test 1: Vérifier que les références ont été supprimées du layout
echo "📋 Test 1: Vérification du layout optimisé\n";

$layoutPath = 'resources/views/layouts/admin_optimized.blade.php';
if (file_exists($layoutPath)) {
    $layoutContent = file_get_contents($layoutPath);
    
    if (strpos($layoutContent, 'cement-orders') === false) {
        echo "✅ Layout: Références 'cement-orders' supprimées\n";
    } else {
        echo "❌ Layout: Références 'cement-orders' encore présentes\n";
    }
    
    if (strpos($layoutContent, 'admin.cement-orders') === false) {
        echo "✅ Layout: Routes 'admin.cement-orders' supprimées\n";
    } else {
        echo "❌ Layout: Routes 'admin.cement-orders' encore présentes\n";
    }
} else {
    echo "❌ Layout optimisé non trouvé\n";
}

echo "\n";

// Test 2: Vérifier que les références ont été supprimées de la vue
echo "👁️  Test 2: Vérification de la vue dashboard\n";

$viewPath = 'resources/views/admin/dashboard_optimized.blade.php';
if (file_exists($viewPath)) {
    $viewContent = file_get_contents($viewPath);
    
    if (strpos($viewContent, 'cement_orders_count') === false) {
        echo "✅ Vue: Références 'cement_orders_count' supprimées\n";
    } else {
        echo "❌ Vue: Références 'cement_orders_count' encore présentes\n";
    }
    
    if (strpos($viewContent, 'monthly_cement_revenue') === false) {
        echo "✅ Vue: Références 'monthly_cement_revenue' supprimées\n";
    } else {
        echo "❌ Vue: Références 'monthly_cement_revenue' encore présentes\n";
    }
} else {
    echo "❌ Vue dashboard optimisée non trouvée\n";
}

echo "\n";

// Test 3: Vérifier que les références ont été supprimées du contrôleur
echo "🎮 Test 3: Vérification du contrôleur\n";

$controllerPath = 'app/Http/Controllers/Admin/DashboardOptimizedController.php';
if (file_exists($controllerPath)) {
    $controllerContent = file_get_contents($controllerPath);
    
    if (strpos($controllerContent, 'CementOrder') === false) {
        echo "✅ Contrôleur: Import 'CementOrder' supprimé\n";
    } else {
        echo "❌ Contrôleur: Import 'CementOrder' encore présent\n";
    }
    
    if (strpos($controllerContent, 'cement_orders') === false) {
        echo "✅ Contrôleur: Requêtes 'cement_orders' supprimées\n";
    } else {
        echo "❌ Contrôleur: Requêtes 'cement_orders' encore présentes\n";
    }
} else {
    echo "❌ Contrôleur optimisé non trouvé\n";
}

echo "\n";

// Test 4: Vérifier que les routes fonctionnent
echo "🛣️  Test 4: Test des routes\n";

try {
    // Simuler un test de route simple
    $routesFile = 'routes/web.php';
    if (file_exists($routesFile)) {
        $routesContent = file_get_contents($routesFile);
        
        if (strpos($routesContent, 'DashboardOptimizedController') !== false) {
            echo "✅ Routes: DashboardOptimizedController référencé\n";
        } else {
            echo "❌ Routes: DashboardOptimizedController non référencé\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Erreur lors du test des routes: " . $e->getMessage() . "\n";
}

echo "\n";

// Instructions finales
echo "🎯 Résultat:\n";
echo "=============\n";
echo "✅ Toutes les références aux 'cement-orders' ont été supprimées\n";
echo "✅ Le dashboard optimisé ne dépend plus de tables inexistantes\n";
echo "✅ L'erreur 'Route [admin.cement-orders.index] not defined' est corrigée\n";

echo "\n🚀 Maintenant testez:\n";
echo "====================\n";
echo "1. Connectez-vous en tant qu'admin\n";
echo "2. Accédez à: /admin/dashboard\n";
echo "3. Vérifiez qu'il n'y a plus d'erreur 500\n";
echo "4. L'interface devrait se charger rapidement\n";

echo "\n💡 Si vous voyez encore des erreurs:\n";
echo "====================================\n";
echo "1. Vérifiez les logs: storage/logs/laravel.log\n";
echo "2. Videz tous les caches:\n";
echo "   php artisan cache:clear\n";
echo "   php artisan route:clear\n";
echo "   php artisan view:clear\n";
echo "   php artisan config:clear\n";

echo "\n✨ Correction terminée!\n";
