<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class AdminPerformanceTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur admin pour les tests
        $this->adminUser = User::factory()->create();
        $this->adminUser->assignRole('admin');
    }

    /**
     * Test que le dashboard optimisé se charge plus rapidement
     */
    public function test_optimized_dashboard_loads_faster()
    {
        $this->actingAs($this->adminUser);

        // Mesurer le temps de chargement du dashboard optimisé
        $startTime = microtime(true);
        $response = $this->get('/admin/dashboard-optimized');
        $optimizedTime = microtime(true) - $startTime;

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard_optimized');

        // Vérifier que le temps de chargement est raisonnable (< 2 secondes)
        $this->assertLessThan(2.0, $optimizedTime, 'Dashboard optimisé trop lent');
    }

    /**
     * Test que le cache fonctionne correctement
     */
    public function test_admin_cache_works()
    {
        $this->actingAs($this->adminUser);

        // Vider le cache
        Cache::forget('admin_optimized_stats');
        Cache::forget('admin_optimized_recent');

        // Premier appel - doit créer le cache
        $startTime = microtime(true);
        $this->get('/admin/dashboard-optimized');
        $firstCallTime = microtime(true) - $startTime;

        // Deuxième appel - doit utiliser le cache
        $startTime = microtime(true);
        $this->get('/admin/dashboard-optimized');
        $secondCallTime = microtime(true) - $startTime;

        // Le deuxième appel doit être plus rapide (utilise le cache)
        $this->assertLessThan($firstCallTime, $secondCallTime, 'Cache non utilisé');
    }

    /**
     * Test que les scripts lourds sont supprimés
     */
    public function test_heavy_scripts_are_removed()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/dashboard-optimized');
        $content = $response->getContent();

        // Vérifier que ApexCharts n'est pas présent
        $this->assertStringNotContainsString('apexcharts', strtolower($content));
        
        // Vérifier que Chart.js est utilisé à la place
        $this->assertStringContainsString('chart.js', strtolower($content));
    }

    /**
     * Test que les timeouts longs sont supprimés
     */
    public function test_long_timeouts_are_removed()
    {
        Config::set('admin_performance.timers.disable_long_timeouts', true);
        Config::set('admin_performance.timers.max_timeout_delay', 1000);

        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/dashboard-optimized');
        $content = $response->getContent();

        // Vérifier qu'il n'y a pas de setTimeout avec des délais > 1000ms
        $this->assertDoesNotMatchRegularExpression('/setTimeout\([^,]+,\s*[2-9]\d{3,}\)/', $content);
    }

    /**
     * Test que les console.log sont supprimés en production
     */
    public function test_console_logs_are_removed_in_production()
    {
        Config::set('app.env', 'production');
        Config::set('admin_performance.logging.disable_console_logs', true);

        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/dashboard-optimized');
        $content = $response->getContent();

        // Vérifier que les console.log sont supprimés
        $this->assertStringNotContainsString('console.log', $content);
        $this->assertStringNotContainsString('console.info', $content);
    }

    /**
     * Test que l'auto-refresh est désactivé
     */
    public function test_auto_refresh_is_disabled()
    {
        Config::set('admin_performance.timers.disable_auto_refresh', true);

        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/dashboard-optimized');
        $content = $response->getContent();

        // Vérifier qu'il n'y a pas de location.reload()
        $this->assertStringNotContainsString('location.reload()', $content);
        
        // Vérifier qu'il n'y a pas de meta refresh
        $this->assertDoesNotMatchRegularExpression('/<meta[^>]*http-equiv=["\']refresh["\'][^>]*>/', $content);
    }

    /**
     * Test que les données sont correctement mises en cache
     */
    public function test_data_caching_works()
    {
        $this->actingAs($this->adminUser);

        // Vider le cache
        Cache::flush();

        // Premier appel
        $this->get('/admin/dashboard-optimized');

        // Vérifier que les données sont en cache
        $this->assertTrue(Cache::has('admin_optimized_stats'));
        $this->assertTrue(Cache::has('admin_optimized_recent'));
    }

    /**
     * Test de comparaison de performance avec cement-manager
     */
    public function test_performance_comparison_with_cement_manager()
    {
        // Créer un utilisateur cement-manager
        $cementUser = User::factory()->create();
        $cementUser->assignRole('cement_manager');

        // Mesurer le temps de chargement cement-manager
        $this->actingAs($cementUser);
        $startTime = microtime(true);
        $cementResponse = $this->get('/cement-manager/dashboard');
        $cementTime = microtime(true) - $startTime;

        // Mesurer le temps de chargement admin optimisé
        $this->actingAs($this->adminUser);
        $startTime = microtime(true);
        $adminResponse = $this->get('/admin/dashboard-optimized');
        $adminTime = microtime(true) - $startTime;

        // Les deux doivent se charger correctement
        $cementResponse->assertStatus(200);
        $adminResponse->assertStatus(200);

        // L'admin optimisé ne doit pas être plus de 50% plus lent que cement-manager
        $maxAllowedTime = $cementTime * 1.5;
        $this->assertLessThan($maxAllowedTime, $adminTime, 
            "Admin optimisé trop lent par rapport à cement-manager (Admin: {$adminTime}s, Cement: {$cementTime}s)");
    }

    /**
     * Test que le middleware de performance fonctionne
     */
    public function test_performance_middleware_works()
    {
        $this->actingAs($this->adminUser);

        $response = $this->get('/admin/dashboard-optimized');
        
        // Vérifier que la réponse contient les optimisations
        $response->assertStatus(200);
        
        // Le middleware doit avoir appliqué les optimisations
        $content = $response->getContent();
        $this->assertStringNotContainsString('setInterval', $content);
    }

    /**
     * Test de l'API de rafraîchissement des stats
     */
    public function test_stats_refresh_api()
    {
        $this->actingAs($this->adminUser);

        $response = $this->post('/admin/dashboard-optimized/refresh');
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }
}
