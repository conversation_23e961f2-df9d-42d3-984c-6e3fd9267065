<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Configuration des Performances Admin
    |--------------------------------------------------------------------------
    |
    | Configuration pour optimiser les performances des vues admin
    | Inspiré de la fluidité des vues cement-manager
    |
    */

    // Activer le mode performance optimisé
    'optimized_mode' => env('ADMIN_OPTIMIZED_MODE', true),

    // Layout à utiliser
    'layout' => env('ADMIN_LAYOUT', 'admin_optimized'), // 'admin' ou 'admin_optimized'

    // Dashboard à utiliser
    'dashboard_controller' => env('ADMIN_DASHBOARD_CONTROLLER', 'optimized'), // 'default' ou 'optimized'

    /*
    |--------------------------------------------------------------------------
    | Scripts et Assets
    |--------------------------------------------------------------------------
    */
    'scripts' => [
        // Désactiver les scripts lourds
        'disable_apexcharts' => env('ADMIN_DISABLE_APEXCHARTS', true),
        'disable_heavy_animations' => env('ADMIN_DISABLE_HEAVY_ANIMATIONS', true),
        'enable_performance_cleaner' => env('ADMIN_ENABLE_PERFORMANCE_CLEANER', true),
        
        // Utiliser Chart.js au lieu d'ApexCharts (plus léger)
        'use_chartjs' => env('ADMIN_USE_CHARTJS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto-refresh et Timers
    |--------------------------------------------------------------------------
    */
    'timers' => [
        // Désactiver l'auto-refresh
        'disable_auto_refresh' => env('ADMIN_DISABLE_AUTO_REFRESH', true),
        
        // Désactiver les setTimeout longs
        'disable_long_timeouts' => env('ADMIN_DISABLE_LONG_TIMEOUTS', true),
        
        // Désactiver les setInterval
        'disable_intervals' => env('ADMIN_DISABLE_INTERVALS', true),
        
        // Délai maximum autorisé pour setTimeout (ms)
        'max_timeout_delay' => env('ADMIN_MAX_TIMEOUT_DELAY', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache et Données
    |--------------------------------------------------------------------------
    */
    'cache' => [
        // Durée de cache pour les statistiques (secondes)
        'stats_cache_duration' => env('ADMIN_STATS_CACHE_DURATION', 14400), // 4 heures
        
        // Durée de cache pour les données récentes (secondes)
        'recent_data_cache_duration' => env('ADMIN_RECENT_DATA_CACHE_DURATION', 3600), // 1 heure
        
        // Utiliser des données statiques pour les graphiques
        'use_static_chart_data' => env('ADMIN_USE_STATIC_CHART_DATA', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging et Debug
    |--------------------------------------------------------------------------
    */
    'logging' => [
        // Désactiver les console.log en production
        'disable_console_logs' => env('ADMIN_DISABLE_CONSOLE_LOGS', true),
        
        // Garder seulement les erreurs et warnings
        'keep_errors_warnings' => env('ADMIN_KEEP_ERRORS_WARNINGS', true),
        
        // Activer le monitoring des performances
        'enable_performance_monitoring' => env('ADMIN_ENABLE_PERFORMANCE_MONITORING', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Interface Utilisateur
    |--------------------------------------------------------------------------
    */
    'ui' => [
        // Simplifier l'interface comme cement-manager
        'simplified_ui' => env('ADMIN_SIMPLIFIED_UI', true),
        
        // Désactiver les animations complexes
        'disable_complex_animations' => env('ADMIN_DISABLE_COMPLEX_ANIMATIONS', true),
        
        // Utiliser des couleurs plates au lieu de gradients
        'use_flat_colors' => env('ADMIN_USE_FLAT_COLORS', true),
        
        // Réduire le nombre d'éléments affichés
        'max_dashboard_items' => env('ADMIN_MAX_DASHBOARD_ITEMS', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Requêtes Base de Données
    |--------------------------------------------------------------------------
    */
    'database' => [
        // Utiliser une seule requête pour les statistiques
        'single_stats_query' => env('ADMIN_SINGLE_STATS_QUERY', true),
        
        // Limiter le nombre de résultats
        'max_recent_items' => env('ADMIN_MAX_RECENT_ITEMS', 5),
        
        // Désactiver les relations lourdes
        'disable_heavy_relations' => env('ADMIN_DISABLE_HEAVY_RELATIONS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Messages et Notifications
    |--------------------------------------------------------------------------
    */
    'messages' => [
        'optimization_enabled' => 'Mode performance admin activé - Interface optimisée comme cement-manager',
        'scripts_cleaned' => 'Scripts lourds nettoyés pour améliorer les performances',
        'cache_refreshed' => 'Cache des données admin rafraîchi',
    ],
];
