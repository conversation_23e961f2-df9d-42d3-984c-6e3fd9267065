<?php

/**
 * <PERSON>ript pour appliquer l'arrêt d'urgence à TOUTES les vues admin
 * Remplacer tous les layouts admin par admin_minimal avec arrêt d'urgence
 */

echo "🚨 APPLICATION ARRÊT D'URGENCE À TOUTES LES VUES ADMIN\n";
echo "=====================================================\n\n";

// Fonction pour scanner récursivement tous les fichiers .blade.php
function scanAdminViews($directory, &$files) {
    if (!is_dir($directory)) return;
    
    $items = scandir($directory);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        
        $path = $directory . DIRECTORY_SEPARATOR . $item;
        
        if (is_dir($path)) {
            scanAdminViews($path, $files);
        } elseif (pathinfo($item, PATHINFO_EXTENSION) === 'php' && 
                  strpos($item, '.blade.php') !== false) {
            $files[] = $path;
        }
    }
}

// Scanner toutes les vues admin
$adminViewsDir = 'resources/views/admin';
$allAdminViews = [];
scanAdminViews($adminViewsDir, $allAdminViews);

echo "🔍 Vues admin trouvées: " . count($allAdminViews) . "\n\n";

$totalFixed = 0;
$alreadyOptimized = 0;

foreach ($allAdminViews as $viewFile) {
    $relativePath = str_replace('resources/views/', '', $viewFile);
    echo "📄 Analyse: $relativePath\n";
    
    if (!file_exists($viewFile)) {
        echo "   ❌ Fichier non trouvé\n";
        continue;
    }
    
    $content = file_get_contents($viewFile);
    $originalContent = $content;
    $modified = false;
    
    // 1. Remplacer @extends('layouts.admin') par @extends('layouts.admin_minimal')
    if (strpos($content, "@extends('layouts.admin')") !== false) {
        $content = str_replace(
            "@extends('layouts.admin')",
            "@extends('layouts.admin_minimal')",
            $content
        );
        echo "   ✅ Layout changé: admin → admin_minimal\n";
        $modified = true;
    }
    
    // 2. Remplacer @extends("layouts.admin") par @extends("layouts.admin_minimal")
    if (strpos($content, '@extends("layouts.admin")') !== false) {
        $content = str_replace(
            '@extends("layouts.admin")',
            '@extends("layouts.admin_minimal")',
            $content
        );
        echo "   ✅ Layout changé: admin → admin_minimal (guillemets doubles)\n";
        $modified = true;
    }
    
    // 3. Vérifier si déjà optimisé
    if (strpos($content, "@extends('layouts.admin_minimal')") !== false) {
        if (!$modified) {
            echo "   ℹ️ Déjà optimisé (utilise admin_minimal)\n";
            $alreadyOptimized++;
        }
    }
    
    // 4. Vérifier les autres layouts admin
    $otherAdminLayouts = [
        "@extends('layouts.admin_dashboard')",
        "@extends('layouts.admin_reports')",
        "@extends('layouts.admin_users')",
        "@extends('layouts.admin_products')"
    ];
    
    foreach ($otherAdminLayouts as $oldLayout) {
        if (strpos($content, $oldLayout) !== false) {
            $content = str_replace(
                $oldLayout,
                "@extends('layouts.admin_minimal')",
                $content
            );
            echo "   ✅ Layout spécialisé remplacé par admin_minimal\n";
            $modified = true;
        }
    }
    
    // 5. Sauvegarder si modifié
    if ($modified) {
        file_put_contents($viewFile, $content);
        echo "   💾 Fichier sauvegardé\n";
        $totalFixed++;
    }
    
    echo "\n";
}

echo "📊 RÉSUMÉ:\n";
echo "=========\n";
echo "✅ Vues corrigées: $totalFixed\n";
echo "ℹ️ Déjà optimisées: $alreadyOptimized\n";
echo "📁 Total analysées: " . count($allAdminViews) . "\n\n";

// Maintenant, s'assurer que le layout admin_minimal a bien l'arrêt d'urgence
echo "🔧 Vérification layout admin_minimal...\n";

$minimalLayoutPath = 'resources/views/layouts/admin_minimal.blade.php';
if (file_exists($minimalLayoutPath)) {
    $layoutContent = file_get_contents($minimalLayoutPath);
    
    if (strpos($layoutContent, 'emergency-kill-all.js') !== false) {
        echo "✅ Arrêt d'urgence présent dans admin_minimal\n";
    } else {
        echo "❌ Arrêt d'urgence manquant dans admin_minimal\n";
        echo "🔧 Ajout de l'arrêt d'urgence...\n";
        
        // Ajouter l'arrêt d'urgence au début des scripts
        $emergencyScript = '    <!-- ARRÊT D\'URGENCE - Charge EN PREMIER pour stopper TOUTES les boucles -->
    <script src="{{ asset(\'js/emergency-kill-all.js\') }}"></script>

';
        
        // Trouver la position après <body> ou avant le premier <script>
        $insertPos = strpos($layoutContent, '<script');
        if ($insertPos !== false) {
            $layoutContent = substr_replace($layoutContent, $emergencyScript, $insertPos, 0);
            file_put_contents($minimalLayoutPath, $layoutContent);
            echo "✅ Arrêt d'urgence ajouté à admin_minimal\n";
        }
    }
} else {
    echo "❌ Layout admin_minimal non trouvé\n";
}

echo "\n";

// Vérifier que le script d'arrêt d'urgence existe
echo "🛑 Vérification script d'arrêt d'urgence...\n";

$emergencyScriptPath = 'public/js/emergency-kill-all.js';
if (file_exists($emergencyScriptPath)) {
    echo "✅ Script emergency-kill-all.js présent\n";
} else {
    echo "❌ Script emergency-kill-all.js manquant\n";
    echo "⚠️ Relancez la création du script d'arrêt d'urgence\n";
}

echo "\n";

// Instructions finales
echo "🎯 RÉSULTATS:\n";
echo "=============\n";

if ($totalFixed > 0) {
    echo "🎉 SUCCÈS! $totalFixed vues admin ont été corrigées\n\n";
    
    echo "✅ Toutes les vues admin utilisent maintenant admin_minimal avec:\n";
    echo "   - Arrêt d'urgence qui charge EN PREMIER\n";
    echo "   - Blocage de toutes les boucles infinies\n";
    echo "   - jQuery local (pas de ERR_CONNECTION_RESET)\n";
    echo "   - Scripts problématiques supprimés\n\n";
    
    echo "🚀 Résultats attendus sur TOUTES les vues admin:\n";
    echo "   - /admin/dashboard → < 5 secondes\n";
    echo "   - /admin/users → < 5 secondes\n";
    echo "   - /admin/supplies → < 5 secondes\n";
    echo "   - /admin/sales → < 5 secondes\n";
    echo "   - /admin/reports → < 5 secondes\n";
    echo "   - Toutes les autres vues → < 5 secondes\n\n";
    
    echo "🧪 TESTEZ MAINTENANT:\n";
    echo "   1. Videz le cache: php artisan cache:clear && php artisan view:clear\n";
    echo "   2. Testez TOUTES les vues admin\n";
    echo "   3. Vous verrez la barre rouge 'ARRÊT D'URGENCE' sur toutes\n";
    echo "   4. Plus de boucles de 40 secondes nulle part\n";
    echo "   5. Console propre sans erreurs\n\n";
    
} else {
    echo "ℹ️ Toutes les vues étaient déjà optimisées\n";
    echo "✅ L'arrêt d'urgence est déjà appliqué partout\n\n";
}

echo "✨ Application terminée!\n";
echo "Toutes les vues admin sont maintenant protégées contre les boucles infinies!\n";
