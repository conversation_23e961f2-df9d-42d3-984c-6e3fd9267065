<?php

/**
 * Script pour basculer entre le dashboard admin original et optimisé
 * Usage: php switch_admin_dashboard.php [optimized|original]
 */

$mode = $argv[1] ?? 'optimized';

echo "🔄 Basculement du Dashboard Admin\n";
echo "=================================\n\n";

$routesFile = 'routes/web.php';

if (!file_exists($routesFile)) {
    echo "❌ Fichier routes/web.php non trouvé\n";
    exit(1);
}

$routesContent = file_get_contents($routesFile);

if ($mode === 'optimized') {
    echo "🚀 Activation du dashboard OPTIMISÉ...\n";
    
    // Remplacer la route principale par la version optimisée
    $routesContent = str_replace(
        "Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');",
        "Route::get('/dashboard', [DashboardOptimizedController::class, 'index'])->name('dashboard');",
        $routesContent
    );
    
    echo "✅ Dashboard principal → Version OPTIMISÉE\n";
    echo "✅ Dashboard original disponible sur /admin/dashboard-original\n";
    
} elseif ($mode === 'original') {
    echo "⚡ Retour au dashboard ORIGINAL...\n";
    
    // Remettre la route principale vers l'original
    $routesContent = str_replace(
        "Route::get('/dashboard', [DashboardOptimizedController::class, 'index'])->name('dashboard');",
        "Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');",
        $routesContent
    );
    
    echo "✅ Dashboard principal → Version ORIGINALE\n";
    echo "✅ Dashboard optimisé disponible sur /admin/dashboard-optimized\n";
    
} else {
    echo "❌ Mode invalide. Utilisez 'optimized' ou 'original'\n";
    echo "Usage: php switch_admin_dashboard.php [optimized|original]\n";
    exit(1);
}

// Sauvegarder les modifications
file_put_contents($routesFile, $routesContent);

// Vider le cache
echo "\n🧹 Nettoyage du cache...\n";
exec('php artisan cache:clear', $output1);
exec('php artisan route:clear', $output2);
exec('php artisan config:clear', $output3);

echo "✅ Cache vidé\n";

echo "\n🎯 RÉSULTAT:\n";
echo "============\n";

if ($mode === 'optimized') {
    echo "✅ Le dashboard admin principal (/admin/dashboard) utilise maintenant la VERSION OPTIMISÉE\n";
    echo "📊 Avantages:\n";
    echo "   • Interface fluide comme cement-manager\n";
    echo "   • Pas de JavaScript lourd qui s'auto-load\n";
    echo "   • Pas de timeouts et logs excessifs\n";
    echo "   • Chart.js au lieu d'ApexCharts (plus léger)\n";
    echo "   • Cache optimisé (4h pour les stats)\n";
    echo "\n🔗 URLs disponibles:\n";
    echo "   • Dashboard optimisé: /admin/dashboard\n";
    echo "   • Dashboard original: /admin/dashboard-original\n";
    echo "   • Dashboard optimisé (alias): /admin/dashboard-optimized\n";
} else {
    echo "✅ Le dashboard admin principal (/admin/dashboard) utilise maintenant la VERSION ORIGINALE\n";
    echo "\n🔗 URLs disponibles:\n";
    echo "   • Dashboard original: /admin/dashboard\n";
    echo "   • Dashboard optimisé: /admin/dashboard-optimized\n";
}

echo "\n💡 Pour tester:\n";
echo "1. Connectez-vous en tant qu'admin\n";
echo "2. Accédez à /admin/dashboard\n";
echo "3. Comparez les performances!\n";

echo "\n✨ Basculement terminé avec succès!\n";
