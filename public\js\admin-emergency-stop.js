/**
 * GRADIS Admin - Arrêt d'Urgence Total
 * Stoppe IMMÉDIATEMENT tous les processus JavaScript
 * Version: 1.0 - Arrêt brutal et efficace
 */

console.log('🚨 GRADIS Admin - Arrêt d\'urgence chargé');

/**
 * Arrêt d'urgence IMMÉDIAT - Stoppe TOUT
 */
function emergencyStopAll() {
    console.log('🛑 ARRÊT D\'URGENCE TOTAL EN COURS...');
    
    // 1. Stopper TOUS les intervals (méthode brutale mais efficace)
    let maxInterval = 0;
    const testInterval = setInterval(() => {}, 1);
    maxInterval = testInterval;
    clearInterval(testInterval);
    
    for (let i = 1; i <= maxInterval + 100; i++) {
        try {
            clearInterval(i);
        } catch (e) {
            // Ignorer les erreurs
        }
    }
    console.log('❌ TOUS les intervals stoppés (1 à', maxInterval + 100, ')');
    
    // 2. Stopper TOUS les timeouts
    let maxTimeout = 0;
    const testTimeout = setTimeout(() => {}, 1);
    maxTimeout = testTimeout;
    clearTimeout(testTimeout);
    
    for (let i = 1; i <= maxTimeout + 100; i++) {
        try {
            clearTimeout(i);
        } catch (e) {
            // Ignorer les erreurs
        }
    }
    console.log('❌ TOUS les timeouts stoppés (1 à', maxTimeout + 100, ')');
    
    // 3. Bloquer définitivement les nouveaux processus
    window.setInterval = function() {
        console.warn('🚫 setInterval BLOQUÉ par arrêt d\'urgence');
        return null;
    };
    
    window.setTimeout = function(callback, delay) {
        // Autoriser seulement les délais très courts (< 100ms)
        if (delay < 100) {
            return window._originalSetTimeout ? window._originalSetTimeout(callback, delay) : null;
        }
        console.warn('🚫 setTimeout BLOQUÉ par arrêt d\'urgence:', delay, 'ms');
        return null;
    };
    
    // 4. Bloquer les requêtes fetch longues
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                console.warn('🚫 Requête annulée (arrêt d\'urgence):', url);
            }, 5000); // 5 secondes max
            
            options.signal = controller.signal;
            
            return originalFetch(url, options)
                .finally(() => clearTimeout(timeoutId));
        };
    }
    
    console.log('✅ ARRÊT D\'URGENCE TERMINÉ - Tous les processus stoppés');
}

/**
 * Surveillance et arrêt automatique
 */
function startEmergencyMonitoring() {
    let processCount = 0;
    
    // Compter les processus existants
    const testInterval = setInterval(() => {}, 1);
    processCount = testInterval;
    clearInterval(testInterval);
    
    console.log('📊 Processus détectés au démarrage:', processCount);
    
    // Si trop de processus, arrêt immédiat
    if (processCount > 50) {
        console.error('🚨 TROP DE PROCESSUS DÉTECTÉS (' + processCount + ') - Arrêt immédiat');
        emergencyStopAll();
        return;
    }
    
    // Surveillance continue (avec un seul timer)
    const monitoringId = setTimeout(() => {
        const currentInterval = setInterval(() => {}, 1);
        clearInterval(currentInterval);
        
        if (currentInterval > processCount + 20) {
            console.error('🚨 EXPLOSION DE PROCESSUS DÉTECTÉE - Arrêt automatique');
            emergencyStopAll();
        }
    }, 3000); // Vérifier après 3 secondes
    
    console.log('👁️ Surveillance d\'urgence activée');
}

/**
 * Créer un bouton d'arrêt d'urgence visible
 */
function createEmergencyButton() {
    const button = document.createElement('button');
    button.id = 'emergency-stop-btn';
    button.innerHTML = '🛑 STOP';
    button.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 99999;
        background: #dc3545;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-weight: bold;
        cursor: pointer;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    `;
    
    button.addEventListener('click', function() {
        emergencyStopAll();
        this.innerHTML = '✅ STOPPÉ';
        this.style.background = '#28a745';
        this.disabled = true;
    });
    
    document.body.appendChild(button);
    console.log('🚨 Bouton d\'arrêt d\'urgence créé');
}

/**
 * Optimisation immédiate au chargement
 */
function immediateOptimization() {
    // Sauvegarder les fonctions natives
    if (!window._originalSetTimeout) {
        window._originalSetTimeout = window.setTimeout;
        window._originalSetInterval = window.setInterval;
        window._originalFetch = window.fetch;
    }
    
    // Désactiver console.log pour réduire le spam
    const originalConsole = window.console;
    window.console = {
        ...originalConsole,
        log: function() {}, // Supprimer les logs
        info: function() {}, // Supprimer les infos
        warn: originalConsole.warn, // Garder les warnings
        error: originalConsole.error // Garder les erreurs
    };
    
    console.warn('⚡ Optimisation immédiate appliquée');
}

/**
 * Initialisation automatique IMMÉDIATE
 */
(function() {
    console.log('🚨 Initialisation arrêt d\'urgence...');
    
    // Optimisation immédiate
    immediateOptimization();
    
    // Surveillance immédiate
    startEmergencyMonitoring();
    
    // Créer le bouton dès que possible
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createEmergencyButton);
    } else {
        createEmergencyButton();
    }
    
    console.log('✅ Arrêt d\'urgence prêt');
})();

// Export global
window.EmergencyStop = {
    stopAll: emergencyStopAll,
    monitor: startEmergencyMonitoring
};
