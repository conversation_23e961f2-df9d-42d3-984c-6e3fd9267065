/*! jQuery v3.7.1 - Minimal version for GRADIS */
(function(global, factory) {
    "use strict";
    if (typeof module === "object" && typeof module.exports === "object") {
        module.exports = global.document ?
            factory(global, true) :
            function(w) {
                if (!w.document) {
                    throw new Error("jQuery requires a window with a document");
                }
                return factory(w);
            };
    } else {
        factory(global);
    }
})(typeof window !== "undefined" ? window : this, function(window, noGlobal) {
    "use strict";
    
    // Version minimale de jQuery pour éviter les erreurs de connexion
    var jQuery = function(selector, context) {
        return new jQuery.fn.init(selector, context);
    };
    
    jQuery.fn = jQuery.prototype = {
        jquery: "3.7.1-minimal",
        constructor: jQuery,
        length: 0,
        
        init: function(selector, context) {
            if (!selector) {
                return this;
            }
            
            if (typeof selector === "string") {
                var elem = document.querySelectorAll(selector);
                for (var i = 0; i < elem.length; i++) {
                    this[i] = elem[i];
                }
                this.length = elem.length;
            } else if (selector.nodeType) {
                this[0] = selector;
                this.length = 1;
            }
            
            return this;
        },
        
        // Méthodes essentielles
        each: function(callback) {
            for (var i = 0; i < this.length; i++) {
                callback.call(this[i], i, this[i]);
            }
            return this;
        },
        
        on: function(events, handler) {
            return this.each(function() {
                this.addEventListener(events, handler);
            });
        },
        
        off: function(events, handler) {
            return this.each(function() {
                this.removeEventListener(events, handler);
            });
        },
        
        click: function(handler) {
            if (handler) {
                return this.on('click', handler);
            } else {
                return this.each(function() {
                    this.click();
                });
            }
        },
        
        val: function(value) {
            if (value !== undefined) {
                return this.each(function() {
                    this.value = value;
                });
            } else {
                return this[0] ? this[0].value : '';
            }
        },
        
        text: function(text) {
            if (text !== undefined) {
                return this.each(function() {
                    this.textContent = text;
                });
            } else {
                return this[0] ? this[0].textContent : '';
            }
        },
        
        html: function(html) {
            if (html !== undefined) {
                return this.each(function() {
                    this.innerHTML = html;
                });
            } else {
                return this[0] ? this[0].innerHTML : '';
            }
        },
        
        addClass: function(className) {
            return this.each(function() {
                this.classList.add(className);
            });
        },
        
        removeClass: function(className) {
            return this.each(function() {
                this.classList.remove(className);
            });
        },
        
        show: function() {
            return this.each(function() {
                this.style.display = '';
            });
        },
        
        hide: function() {
            return this.each(function() {
                this.style.display = 'none';
            });
        }
    };
    
    jQuery.fn.init.prototype = jQuery.fn;
    
    // Fonctions statiques essentielles
    jQuery.extend = function() {
        var target = arguments[0] || {};
        for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source) {
                if (source.hasOwnProperty(key)) {
                    target[key] = source[key];
                }
            }
        }
        return target;
    };
    
    jQuery.ready = function(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    };
    
    // Alias
    var $ = jQuery;
    
    // Export
    if (!noGlobal) {
        window.jQuery = window.$ = jQuery;
    }
    
    return jQuery;
});

console.log('✅ jQuery minimal local chargé (évite ERR_CONNECTION_RESET)');
