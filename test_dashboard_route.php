<?php

/**
 * Test simple pour vérifier que la route dashboard optimisé fonctionne
 */

require_once 'vendor/autoload.php';

echo "🧪 Test de la Route Dashboard Optimisé\n";
echo "=====================================\n\n";

// Test 1: Vérifier que le contrôleur existe et peut être instancié
echo "📋 Test 1: Vérification du contrôleur\n";

try {
    $controllerClass = 'App\Http\Controllers\Admin\DashboardOptimizedController';
    
    if (class_exists($controllerClass)) {
        echo "✅ Classe contrôleur trouvée: $controllerClass\n";
        
        // Vérifier que la méthode index existe
        if (method_exists($controllerClass, 'index')) {
            echo "✅ Méthode index() trouvée\n";
        } else {
            echo "❌ Méthode index() manquante\n";
        }
        
        // Vérifier que la méthode refreshStats existe
        if (method_exists($controllerClass, 'refreshStats')) {
            echo "✅ Méthode refreshStats() trouvée\n";
        } else {
            echo "❌ Méthode refreshStats() manquante\n";
        }
        
    } else {
        echo "❌ Classe contrôleur non trouvée: $controllerClass\n";
    }
} catch (Exception $e) {
    echo "❌ Erreur lors de la vérification du contrôleur: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Vérifier que la vue existe
echo "👁️  Test 2: Vérification de la vue\n";

$viewPath = 'resources/views/admin/dashboard_optimized.blade.php';
if (file_exists($viewPath)) {
    echo "✅ Vue trouvée: $viewPath\n";
    
    // Vérifier le contenu de la vue
    $viewContent = file_get_contents($viewPath);
    
    if (strpos($viewContent, "@extends('layouts.admin_optimized')") !== false) {
        echo "✅ Vue étend le bon layout\n";
    } else {
        echo "❌ Vue n'étend pas le bon layout\n";
    }
    
    if (strpos($viewContent, 'Chart.js') !== false) {
        echo "✅ Chart.js détecté dans la vue\n";
    } else {
        echo "❌ Chart.js non détecté dans la vue\n";
    }
    
} else {
    echo "❌ Vue non trouvée: $viewPath\n";
}

echo "\n";

// Test 3: Vérifier que le layout optimisé existe
echo "🎨 Test 3: Vérification du layout optimisé\n";

$layoutPath = 'resources/views/layouts/admin_optimized.blade.php';
if (file_exists($layoutPath)) {
    echo "✅ Layout optimisé trouvé: $layoutPath\n";
} else {
    echo "❌ Layout optimisé non trouvé: $layoutPath\n";
}

echo "\n";

// Test 4: Vérifier les routes dans web.php
echo "🛣️  Test 4: Vérification des routes\n";

if (file_exists('routes/web.php')) {
    $routesContent = file_get_contents('routes/web.php');
    
    if (strpos($routesContent, 'dashboard-optimized') !== false) {
        echo "✅ Route dashboard-optimized trouvée dans web.php\n";
    } else {
        echo "❌ Route dashboard-optimized non trouvée dans web.php\n";
    }
    
    if (strpos($routesContent, 'DashboardOptimizedController') !== false) {
        echo "✅ Contrôleur référencé dans web.php\n";
    } else {
        echo "❌ Contrôleur non référencé dans web.php\n";
    }
    
} else {
    echo "❌ Fichier routes/web.php non trouvé\n";
}

echo "\n";

// Test 5: Vérifier le middleware
echo "🛡️  Test 5: Vérification du middleware\n";

if (file_exists('app/Http/Kernel.php')) {
    $kernelContent = file_get_contents('app/Http/Kernel.php');
    
    if (strpos($kernelContent, 'admin.performance') !== false) {
        echo "✅ Middleware admin.performance enregistré\n";
    } else {
        echo "❌ Middleware admin.performance non enregistré\n";
    }
} else {
    echo "❌ Fichier app/Http/Kernel.php non trouvé\n";
}

echo "\n";

// Test 6: Instructions pour tester manuellement
echo "🔧 Instructions pour tester manuellement:\n";
echo "=========================================\n";
echo "1. Assurez-vous que le serveur Laravel fonctionne:\n";
echo "   php artisan serve\n\n";
echo "2. Connectez-vous en tant qu'administrateur\n\n";
echo "3. Accédez à l'une de ces URLs:\n";
echo "   - http://localhost:8000/admin/dashboard-optimized\n";
echo "   - http://votre-domaine.com/admin/dashboard-optimized\n\n";
echo "4. Si vous obtenez une erreur 404, vérifiez:\n";
echo "   - Que vous êtes bien connecté en tant qu'admin\n";
echo "   - Que le cache a été vidé: php artisan cache:clear\n";
echo "   - Que les routes sont bien chargées: php artisan route:list\n\n";

echo "✨ Test terminé!\n";
echo "\n💡 Si la route n'est toujours pas accessible:\n";
echo "1. Vérifiez les logs Laravel: storage/logs/laravel.log\n";
echo "2. Activez le debug dans .env: APP_DEBUG=true\n";
echo "3. Vérifiez que vous avez le rôle 'admin'\n";
