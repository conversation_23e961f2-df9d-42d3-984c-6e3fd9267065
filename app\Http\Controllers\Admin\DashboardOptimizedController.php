<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Product;
use App\Models\Supply;
use App\Models\Order;


class DashboardOptimizedController extends Controller
{
    /**
     * Dashboard admin ultra-optimisé - Inspiré de la simplicité cement-manager
     */
    public function index()
    {
        // Cache ultra-long pour les statistiques de base (4 heures)
        $stats = Cache::remember('admin_optimized_stats', 14400, function () {
            // Une seule requête optimisée pour toutes les statistiques essentielles
            // Utilisation de try-catch pour gérer les tables qui pourraient ne pas exister
            try {
                $basicStats = DB::selectOne('
                    SELECT
                        (SELECT COUNT(*) FROM users) as total_users,
                        (SELECT COUNT(*) FROM products) as total_products,
                        (SELECT COALESCE(COUNT(*), 0) FROM orders) as total_orders,
                        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())) as monthly_revenue,

                ');
            } catch (\Exception $e) {
                // En cas d'erreur, utiliser des valeurs par défaut
                $basicStats = (object) [
                    'total_users' => User::count(),
                    'total_products' => Product::count(),
                    'total_orders' => 0,
                    'monthly_revenue' => 0,
                ];
            }

            return [
                'total_users' => $basicStats->total_users ?? 0,
                'total_products' => $basicStats->total_products ?? 0,
                'total_orders' => $basicStats->total_orders ?? 0,
                'monthly_revenue' => $basicStats->monthly_revenue ?? 0,
            ];
        });

        // Cache pour les données récentes (1 heure)
        $recentData = Cache::remember('admin_optimized_recent', 3600, function () {
            // Requêtes minimales et optimisées avec gestion d'erreurs
            try {
                $pendingSupplies = Supply::select('id', 'reference', 'total_amount', 'status', 'created_at')
                    ->where('status', 'pending')
                    ->latest()
                    ->limit(5)
                    ->get();
            } catch (\Exception $e) {
                $pendingSupplies = collect(); // Collection vide en cas d'erreur
            }

            try {
                $latestUsers = User::select('id', 'name', 'email', 'created_at')
                    ->latest()
                    ->limit(5)
                    ->get();
            } catch (\Exception $e) {
                $latestUsers = collect(); // Collection vide en cas d'erreur
            }

            return [
                'pendingSupplies' => $pendingSupplies,
                'latest_users' => $latestUsers,
            ];
        });

        // Données statiques pour les graphiques (évite les requêtes lourdes)
        $chartData = [
            'monthlyRevenue' => [15000, 25000, 20000, 30000, 28000, 35000],
            'months' => ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'categoryData' => [45, 30, 15, 10],
            'categoryLabels' => ['Ciment', 'Fer', 'Sable', 'Gravier']
        ];

        return view('admin.dashboard_optimized', array_merge(
            compact('stats', 'chartData'),
            $recentData
        ));
    }

    /**
     * API pour rafraîchir les statistiques (optionnel)
     */
    public function refreshStats()
    {
        Cache::forget('admin_optimized_stats');
        Cache::forget('admin_optimized_recent');
        
        return response()->json(['success' => true, 'message' => 'Statistiques rafraîchies']);
    }
}
