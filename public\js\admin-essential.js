/**
 * GRADIS Admin - Scripts Essentiels Ultra-Légers
 * Version: 1.0 - Aucun processus en arrière-plan
 * Inspiré de la simplicité cement-manager
 */

console.log('⚡ GRADIS Admin - Scripts essentiels chargés');

/**
 * Configuration ultra-légère
 */
const ADMIN_ESSENTIAL = {
    // Pas d'auto-refresh
    AUTO_REFRESH_ENABLED: false,
    
    // Animations CSS uniquement
    USE_CSS_ANIMATIONS: true,
    
    // Pas de polling
    DISABLE_POLLING: true
};

/**
 * Fonction utilitaire pour les notifications simples
 */
function showSimpleNotification(message, type = 'info') {
    // Utiliser les alertes Bootstrap existantes
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    // Auto-suppression après 3 secondes (délai court)
    setTimeout(() => {
        if (alert && alert.parentNode) {
            alert.remove();
        }
    }, 3000);
}

/**
 * Fonction pour actualiser une table sans auto-refresh
 */
function refreshTable() {
    console.log('🔄 Actualisation manuelle de la table');
    window.location.reload();
}

/**
 * Optimisation des formulaires
 */
function optimizeForms() {
    // Désactiver la double soumission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Traitement...';
            }
        });
    });
}

/**
 * Optimisation des tableaux
 */
function optimizeTables() {
    // Ajouter des classes CSS pour l'optimisation
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        table.classList.add('table-optimized');
    });
}

/**
 * Gestion des modals Bootstrap
 */
function initModals() {
    // Nettoyer les modals à la fermeture
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('hidden.bs.modal', function() {
            // Nettoyer le contenu dynamique
            const dynamicContent = modal.querySelectorAll('.dynamic-content');
            dynamicContent.forEach(content => {
                content.innerHTML = '';
            });
        });
    });
}

/**
 * Initialisation ultra-légère
 */
function initEssentialAdmin() {
    console.log('🚀 Initialisation des scripts essentiels...');
    
    // Optimisations de base
    optimizeForms();
    optimizeTables();
    initModals();
    
    console.log('✅ Scripts essentiels initialisés');
}

/**
 * Bloquer les processus lourds (sans créer de nouveaux processus)
 */
function blockHeavyProcesses() {
    // Bloquer seulement les intervals très courts (< 1000ms)
    const originalSetInterval = window.setInterval;
    window.setInterval = function(callback, delay, ...args) {
        if (delay < 1000) {
            console.warn('⚠️ Interval court bloqué:', delay, 'ms');
            return null;
        }
        return originalSetInterval.call(this, callback, delay, ...args);
    };
    
    // Limiter les timeouts très longs (> 30 secondes)
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
        if (delay > 30000) {
            console.warn('⚠️ Timeout très long limité:', delay, 'ms → 30000ms');
            delay = 30000;
        }
        return originalSetTimeout.call(this, callback, delay, ...args);
    };
}

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', function() {
    // Bloquer les processus lourds AVANT tout
    blockHeavyProcesses();
    
    // Initialiser les fonctionnalités essentielles
    initEssentialAdmin();
    
    console.log('🎉 Admin essentiel prêt!');
});

// Export global minimal
window.AdminEssential = {
    showNotification: showSimpleNotification,
    refreshTable: refreshTable,
    config: ADMIN_ESSENTIAL
};
