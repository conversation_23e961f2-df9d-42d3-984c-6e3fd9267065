/**
 * jQuery Fallback - Version simplifiée pour GRADIS
 * Fournit les fonctions jQuery essentielles si jQuery ne se charge pas
 * Version: 1.0 - Fallback minimal
 */

// Vérifier si jQuery est déjà chargé
if (typeof jQuery === 'undefined' && typeof $ === 'undefined') {
    console.warn('⚠️ jQuery non disponible, chargement du fallback...');
    
    // Créer un objet jQuery minimal
    window.$ = window.jQuery = function(selector) {
        if (typeof selector === 'string') {
            return {
                length: 0,
                each: function(callback) { return this; },
                on: function(event, handler) {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.addEventListener(event, handler));
                    return this;
                },
                click: function(handler) {
                    return this.on('click', handler);
                },
                ready: function(callback) {
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', callback);
                    } else {
                        callback();
                    }
                    return this;
                },
                addClass: function(className) {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.classList.add(className));
                    return this;
                },
                removeClass: function(className) {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.classList.remove(className));
                    return this;
                },
                hide: function() {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.style.display = 'none');
                    return this;
                },
                show: function() {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.style.display = '');
                    return this;
                },
                html: function(content) {
                    if (content !== undefined) {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => el.innerHTML = content);
                        return this;
                    }
                    const element = document.querySelector(selector);
                    return element ? element.innerHTML : '';
                },
                text: function(content) {
                    if (content !== undefined) {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => el.textContent = content);
                        return this;
                    }
                    const element = document.querySelector(selector);
                    return element ? element.textContent : '';
                },
                val: function(value) {
                    if (value !== undefined) {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => el.value = value);
                        return this;
                    }
                    const element = document.querySelector(selector);
                    return element ? element.value : '';
                },
                attr: function(name, value) {
                    if (value !== undefined) {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => el.setAttribute(name, value));
                        return this;
                    }
                    const element = document.querySelector(selector);
                    return element ? element.getAttribute(name) : null;
                },
                css: function(property, value) {
                    if (value !== undefined) {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => el.style[property] = value);
                        return this;
                    }
                    const element = document.querySelector(selector);
                    return element ? getComputedStyle(element)[property] : null;
                },
                find: function(childSelector) {
                    return $(selector + ' ' + childSelector);
                },
                parent: function() {
                    const element = document.querySelector(selector);
                    return element && element.parentElement ? $(element.parentElement) : $();
                },
                remove: function() {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.remove());
                    return this;
                },
                append: function(content) {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        if (typeof content === 'string') {
                            el.insertAdjacentHTML('beforeend', content);
                        } else {
                            el.appendChild(content);
                        }
                    });
                    return this;
                },
                prepend: function(content) {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        if (typeof content === 'string') {
                            el.insertAdjacentHTML('afterbegin', content);
                        } else {
                            el.insertBefore(content, el.firstChild);
                        }
                    });
                    return this;
                }
            };
        } else if (typeof selector === 'function') {
            // $(document).ready() equivalent
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', selector);
            } else {
                selector();
            }
            return this;
        }
        return {};
    };
    
    // Ajouter les méthodes statiques essentielles
    $.fn = $.prototype = {};
    
    $.extend = function(target, ...sources) {
        sources.forEach(source => {
            if (source) {
                Object.keys(source).forEach(key => {
                    target[key] = source[key];
                });
            }
        });
        return target;
    };
    
    $.each = function(obj, callback) {
        if (Array.isArray(obj)) {
            obj.forEach((item, index) => callback.call(item, index, item));
        } else {
            Object.keys(obj).forEach(key => callback.call(obj[key], key, obj[key]));
        }
        return obj;
    };
    
    $.ajax = function(options) {
        const defaults = {
            method: 'GET',
            url: '',
            data: null,
            success: function() {},
            error: function() {},
            complete: function() {}
        };
        
        const settings = $.extend({}, defaults, options);
        
        fetch(settings.url, {
            method: settings.method,
            body: settings.data,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            settings.success(data);
            settings.complete();
        })
        .catch(error => {
            settings.error(error);
            settings.complete();
        });
    };
    
    // Méthodes pour les événements de document
    $(document).ready = function(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    };
    
    console.log('✅ jQuery fallback chargé avec succès');
} else {
    console.log('✅ jQuery déjà disponible');
}

// Export pour compatibilité
if (typeof module !== 'undefined' && module.exports) {
    module.exports = $;
}
