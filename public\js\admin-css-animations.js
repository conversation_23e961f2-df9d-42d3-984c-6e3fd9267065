/**
 * GRADIS Admin - Animations CSS Pures
 * Remplace tous les setTimeout d'animations par du CSS pur
 * Version: 1.0 - Zéro processus en arrière-plan
 */

console.log('🎨 GRADIS Admin - Animations CSS pures chargées');

/**
 * CSS Animations pour remplacer les setTimeout
 */
const CSS_ANIMATIONS = `
<style id="admin-css-animations">
/* Animations de base */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideOutDown {
    from { 
        opacity: 1; 
        transform: translateY(0); 
    }
    to { 
        opacity: 0; 
        transform: translateY(30px); 
    }
}

@keyframes slideOutLeft {
    from { 
        opacity: 1; 
        transform: translateX(0); 
    }
    to { 
        opacity: 0; 
        transform: translateX(-100%); 
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* Classes utilitaires */
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.animate-slide-out-down {
    animation: slideOutDown 0.3s ease-in;
}

.animate-slide-out-left {
    animation: slideOutLeft 0.5s ease-in;
}

.animate-pulse {
    animation: pulse 0.3s ease-in-out;
}

.animate-button-press {
    animation: buttonPress 0.15s ease-in-out;
}

/* Transitions fluides */
.smooth-transition {
    transition: all 0.3s ease;
}

.fast-transition {
    transition: all 0.15s ease;
}

/* Optimisations pour les tables */
.table-row-hover {
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.table-row-hover:hover {
    background-color: #f8f9fa !important;
    transform: translateX(2px);
}

/* Optimisations pour les boutons */
.btn-optimized {
    transition: all 0.2s ease;
}

.btn-optimized:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-optimized:active {
    transform: translateY(0);
}

/* Optimisations pour les cartes */
.card-optimized {
    transition: all 0.3s ease;
}

.card-optimized:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

/* Animations pour les filtres */
.filter-section {
    transition: all 0.3s ease;
}

.filter-section.animate-in {
    animation: slideInUp 0.3s ease-out;
}

/* Animations pour les modals */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

/* Désactiver les animations si l'utilisateur préfère */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
</style>
`;

/**
 * Injecter les animations CSS
 */
function injectCSSAnimations() {
    if (!document.getElementById('admin-css-animations')) {
        document.head.insertAdjacentHTML('beforeend', CSS_ANIMATIONS);
        console.log('✅ Animations CSS injectées');
    }
}

/**
 * Remplacer les setTimeout par des classes CSS
 */
function replaceTimeoutAnimations() {
    // Intercepter les setTimeout d'animations courtes
    const originalSetTimeout = window.setTimeout;
    
    window.setTimeout = function(callback, delay, ...args) {
        // Si c'est un délai d'animation court (< 1000ms), on l'exécute immédiatement
        if (delay <= 1000) {
            // Exécuter immédiatement pour éviter les délais
            callback();
            return null;
        }
        
        // Garder les timeouts longs (> 1000ms) mais les limiter
        if (delay > 30000) {
            delay = 30000;
        }
        
        return originalSetTimeout.call(this, callback, delay, ...args);
    };
    
    console.log('✅ setTimeout d\'animations remplacés par CSS');
}

/**
 * Optimiser les animations existantes
 */
function optimizeExistingAnimations() {
    // Ajouter les classes optimisées aux éléments existants
    const tables = document.querySelectorAll('table tbody tr');
    tables.forEach(row => {
        row.classList.add('table-row-hover');
    });
    
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.classList.add('btn-optimized');
    });
    
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.classList.add('card-optimized');
    });
    
    const filterSections = document.querySelectorAll('.filter-section');
    filterSections.forEach(section => {
        section.classList.add('smooth-transition');
    });
    
    console.log('✅ Éléments existants optimisés');
}

/**
 * Fonctions utilitaires pour remplacer les animations JavaScript
 */
window.AdminAnimations = {
    // Remplace les animations de suppression
    deleteRow: function(element) {
        element.classList.add('animate-slide-out-left');
        // Pas de setTimeout, on utilise l'événement animationend
        element.addEventListener('animationend', function() {
            element.remove();
        }, { once: true });
    },
    
    // Remplace les animations d'apparition
    showElement: function(element) {
        element.style.display = '';
        element.classList.add('animate-slide-in-up');
    },
    
    // Remplace les animations de masquage
    hideElement: function(element) {
        element.classList.add('animate-slide-out-down');
        element.addEventListener('animationend', function() {
            element.style.display = 'none';
            element.classList.remove('animate-slide-out-down');
        }, { once: true });
    },
    
    // Remplace les effets de bouton
    pressButton: function(button) {
        button.classList.add('animate-button-press');
        button.addEventListener('animationend', function() {
            button.classList.remove('animate-button-press');
        }, { once: true });
    },
    
    // Remplace les effets de pulse
    pulseElement: function(element) {
        element.classList.add('animate-pulse');
        element.addEventListener('animationend', function() {
            element.classList.remove('animate-pulse');
        }, { once: true });
    }
};

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', function() {
    // Injecter les CSS d'abord
    injectCSSAnimations();
    
    // Remplacer les setTimeout
    replaceTimeoutAnimations();
    
    // Optimiser les éléments existants
    setTimeout(() => {
        optimizeExistingAnimations();
    }, 100); // Délai minimal pour laisser le DOM se charger
    
    console.log('🎉 Animations CSS pures activées!');
});

console.log('⚡ Admin CSS Animations prêt!');
