/**
 * GRADIS Admin - Script Stable et Optimisé
 * Version unifiée pour des vues dynamiques et rapides
 * Version: 2.0 - Stable et performant
 */

console.log('🚀 GRADIS Admin - Script stable chargé');

/**
 * Configuration globale stable
 */
const STABLE_CONFIG = {
    // Délais optimisés mais sûrs
    ANIMATION_DELAY: 150,
    NOTIFICATION_DELAY: 200,
    SEARCH_DEBOUNCE: 300,
    
    // Performance équilibrée
    ENABLE_ANIMATIONS: true,
    ENABLE_TOOLTIPS: true,
    ENABLE_CHARTS: true,
    
    // Sécurité
    SUPPRESS_EXTENSION_ERRORS: true,
    FALLBACK_ENABLED: true
};

/**
 * Gestionnaire d'erreurs stable
 */
function setupErrorHandling() {
    // Supprimer les erreurs d'extensions de navigateur
    const originalError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
        // Supprimer les erreurs connues d'extensions
        if (message && (
            message.includes("inpage.js") ||
            message.includes("Cannot read properties of null") ||
            message.includes("reading 'type'") ||
            source && source.includes("extension")
        )) {
            return true; // Supprimer l'erreur
        }
        
        // Garder les autres erreurs pour le débogage
        if (originalError) {
            return originalError(message, source, lineno, colno, error);
        }
        return false;
    };
    
    // Gérer les promesses rejetées
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message && 
            event.reason.message.includes('inpage.js')) {
            event.preventDefault();
        }
    });
    
    console.log('✅ Gestion d\'erreurs stable activée');
}

/**
 * Optimisations de performance équilibrées
 */
function setupPerformanceOptimizations() {
    // Intercepter seulement les timeouts vraiment problématiques
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
        // Permettre les délais normaux (< 30 secondes)
        if (delay <= 30000) {
            return originalSetTimeout.call(this, callback, delay, ...args);
        }
        
        // Limiter seulement les délais excessifs
        console.warn('⚠️ Timeout excessif limité:', delay, 'ms → 30000ms');
        return originalSetTimeout.call(this, callback, 30000, ...args);
    };
    
    // Intercepter seulement les intervals vraiment courts
    const originalSetInterval = window.setInterval;
    window.setInterval = function(callback, delay, ...args) {
        // Bloquer seulement les intervals très courts (< 500ms)
        if (delay < 500) {
            console.warn('⚠️ Interval très court bloqué:', delay, 'ms');
            return null;
        }
        
        return originalSetInterval.call(this, callback, delay, ...args);
    };
    
    console.log('✅ Optimisations de performance équilibrées activées');
}

/**
 * Fonctions utilitaires stables
 */
const StableUtils = {
    // Debounce stable
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Animation sûre
    animateElement: function(element, className = 'fade-in') {
        if (!element || !STABLE_CONFIG.ENABLE_ANIMATIONS) return;
        
        element.classList.add(className);
        setTimeout(() => {
            element.classList.remove(className);
        }, 1000);
    },
    
    // Notification stable
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        `;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-suppression après 5 secondes
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    },
    
    // Requête AJAX sécurisée
    safeFetch: function(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);
        
        options.signal = controller.signal;
        
        return fetch(url, options)
            .finally(() => clearTimeout(timeoutId))
            .catch(error => {
                if (error.name === 'AbortError') {
                    console.warn('⚠️ Requête annulée (timeout):', url);
                } else {
                    console.error('❌ Erreur requête:', error);
                }
                throw error;
            });
    }
};

/**
 * Initialisation des tooltips Bootstrap
 */
function initTooltips() {
    if (!STABLE_CONFIG.ENABLE_TOOLTIPS) return;
    
    try {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        console.log('✅ Tooltips initialisés');
    } catch (error) {
        console.warn('⚠️ Erreur initialisation tooltips:', error.message);
    }
}

/**
 * Fonctions pour les tables dynamiques
 */
const TableUtils = {
    // Fonction refreshTable globale pour éviter les erreurs
    refreshTable: function(tableId = null) {
        console.log('🔄 Actualisation de la table:', tableId || 'toutes');
        
        // Simuler un refresh avec animation
        const tables = tableId ? [document.getElementById(tableId)] : document.querySelectorAll('table');
        
        tables.forEach(table => {
            if (table) {
                table.style.opacity = '0.7';
                setTimeout(() => {
                    table.style.opacity = '1';
                    StableUtils.showNotification('Table actualisée avec succès', 'success');
                }, 500);
            }
        });
    },
    
    // Recherche dans les tables
    searchTable: StableUtils.debounce(function(searchTerm, tableId) {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const isVisible = text.includes(searchTerm.toLowerCase());
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) visibleCount++;
        });
        
        console.log(`🔍 Recherche: ${visibleCount} résultats trouvés`);
    }, STABLE_CONFIG.SEARCH_DEBOUNCE),
    
    // Tri des tables
    sortTable: function(tableId, columnIndex, direction = 'asc') {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            if (direction === 'asc') {
                return aText.localeCompare(bText);
            } else {
                return bText.localeCompare(aText);
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
        console.log(`📊 Table triée par colonne ${columnIndex} (${direction})`);
    }
};

/**
 * Gestionnaire de formulaires stable
 */
const FormUtils = {
    // Validation en temps réel
    setupRealTimeValidation: function(formId) {
        const form = document.getElementById(formId);
        if (!form) return;
        
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                FormUtils.validateField(this);
            });
        });
        
        console.log('✅ Validation temps réel activée pour:', formId);
    },
    
    // Validation d'un champ
    validateField: function(field) {
        const isValid = field.checkValidity();
        field.classList.toggle('is-valid', isValid);
        field.classList.toggle('is-invalid', !isValid);
        
        return isValid;
    },
    
    // Soumission sécurisée
    safeSubmit: function(formId, callback) {
        const form = document.getElementById(formId);
        if (!form) return;
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const isValid = FormUtils.validateForm(this);
            if (isValid) {
                if (callback) callback(this);
            } else {
                StableUtils.showNotification('Veuillez corriger les erreurs du formulaire', 'warning');
            }
        });
    },
    
    // Validation complète du formulaire
    validateForm: function(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!FormUtils.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
};

/**
 * Initialisation principale
 */
function initStableAdmin() {
    console.log('🚀 Initialisation de l\'admin stable...');
    
    // Configuration de base
    setupErrorHandling();
    setupPerformanceOptimizations();
    
    // Initialisation des composants
    setTimeout(() => {
        initTooltips();
        
        // Exposer les fonctions globalement pour éviter les erreurs
        window.refreshTable = TableUtils.refreshTable;
        window.searchTable = TableUtils.searchTable;
        window.sortTable = TableUtils.sortTable;
        
        console.log('✅ Admin stable initialisé avec succès');
        StableUtils.showNotification('Interface optimisée et stabilisée', 'success');
    }, STABLE_CONFIG.ANIMATION_DELAY);
}

/**
 * Initialisation automatique
 */
document.addEventListener('DOMContentLoaded', initStableAdmin);

// Initialisation immédiate si le DOM est déjà prêt
if (document.readyState !== 'loading') {
    initStableAdmin();
}

// Export global des utilitaires
window.StableUtils = StableUtils;
window.TableUtils = TableUtils;
window.FormUtils = FormUtils;
window.STABLE_CONFIG = STABLE_CONFIG;

console.log('⚡ Script admin stable prêt');
