/**
 * GRADIS Admin - Détecteur de Boucles Infinies
 * Détecte et stoppe les boucles qui causent les 40 secondes de chargement
 * Version: 1.0 - Détection et arrêt automatique
 */

console.log('🔍 GRADIS Admin - Détecteur de boucles chargé');

/**
 * Détecteur de boucles infinies
 */
class LoopDetector {
    constructor() {
        this.timeoutCount = 0;
        this.intervalCount = 0;
        this.recursionCount = {};
        this.startTime = Date.now();
        this.maxExecutionTime = 10000; // 10 secondes max
        this.isMonitoring = true;
        
        this.init();
    }
    
    init() {
        this.interceptTimers();
        this.monitorExecution();
        this.createDashboard();
    }
    
    /**
     * Intercepter et surveiller les timers
     */
    interceptTimers() {
        const self = this;
        
        // Sauvegarder les fonctions originales
        if (!window._originalSetTimeout) {
            window._originalSetTimeout = window.setTimeout;
            window._originalSetInterval = window.setInterval;
        }
        
        // Intercepter setTimeout
        window.setTimeout = function(callback, delay, ...args) {
            self.timeoutCount++;
            
            // Détecter les récursions suspectes
            const stack = new Error().stack;
            const functionName = self.extractFunctionName(stack);
            
            if (functionName) {
                self.recursionCount[functionName] = (self.recursionCount[functionName] || 0) + 1;
                
                if (self.recursionCount[functionName] > 10) {
                    console.error('🚨 RÉCURSION INFINIE DÉTECTÉE:', functionName, 'appelée', self.recursionCount[functionName], 'fois');
                    self.showAlert('RÉCURSION INFINIE', `Fonction ${functionName} appelée ${self.recursionCount[functionName]} fois`);
                    return null; // Bloquer l'exécution
                }
            }
            
            // Limiter les délais courts répétitifs
            if (delay < 200 && self.timeoutCount > 50) {
                console.warn('🚫 setTimeout court bloqué (trop de répétitions):', delay, 'ms');
                return null;
            }
            
            self.updateDashboard();
            return window._originalSetTimeout.call(this, callback, delay, ...args);
        };
        
        // Intercepter setInterval
        window.setInterval = function(callback, delay, ...args) {
            self.intervalCount++;
            
            if (self.intervalCount > 5) {
                console.error('🚨 TROP D\'INTERVALS DÉTECTÉS:', self.intervalCount);
                self.showAlert('TROP D\'INTERVALS', `${self.intervalCount} intervals actifs`);
                return null; // Bloquer l'exécution
            }
            
            self.updateDashboard();
            return window._originalSetInterval.call(this, callback, delay, ...args);
        };
    }
    
    /**
     * Surveiller le temps d'exécution global
     */
    monitorExecution() {
        const self = this;
        
        const monitor = setInterval(() => {
            const elapsed = Date.now() - self.startTime;
            
            if (elapsed > self.maxExecutionTime && self.isMonitoring) {
                console.error('🚨 TEMPS D\'EXÉCUTION DÉPASSÉ:', elapsed, 'ms');
                self.showAlert('TEMPS DÉPASSÉ', `Exécution > ${self.maxExecutionTime}ms`);
                self.emergencyStop();
                clearInterval(monitor);
            }
            
            self.updateDashboard();
        }, 1000);
        
        // Auto-arrêt après 30 secondes
        setTimeout(() => {
            if (self.isMonitoring) {
                console.log('✅ Surveillance terminée - Pas de boucles détectées');
                self.isMonitoring = false;
                clearInterval(monitor);
            }
        }, 30000);
    }
    
    /**
     * Extraire le nom de fonction depuis la stack trace
     */
    extractFunctionName(stack) {
        const lines = stack.split('\n');
        for (let line of lines) {
            const match = line.match(/at\s+(\w+)/);
            if (match && match[1] !== 'setTimeout' && match[1] !== 'setInterval') {
                return match[1];
            }
        }
        return null;
    }
    
    /**
     * Arrêt d'urgence
     */
    emergencyStop() {
        console.log('🛑 ARRÊT D\'URGENCE EN COURS...');
        
        // Stopper tous les timers
        let maxTimeout = setTimeout(() => {}, 1);
        for (let i = 1; i <= maxTimeout + 50; i++) {
            try { clearTimeout(i); } catch (e) {}
        }
        
        let maxInterval = setInterval(() => {}, 1);
        clearInterval(maxInterval);
        for (let i = 1; i <= maxInterval + 50; i++) {
            try { clearInterval(i); } catch (e) {}
        }
        
        // Bloquer définitivement
        window.setTimeout = () => null;
        window.setInterval = () => null;
        
        this.showAlert('ARRÊT D\'URGENCE', 'Tous les processus ont été stoppés');
        console.log('✅ ARRÊT D\'URGENCE TERMINÉ');
    }
    
    /**
     * Créer le tableau de bord de surveillance
     */
    createDashboard() {
        const dashboard = document.createElement('div');
        dashboard.id = 'loop-detector-dashboard';
        dashboard.style.cssText = `
            position: fixed;
            top: 50px;
            right: 10px;
            z-index: 99999;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            min-width: 200px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        
        dashboard.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">🔍 Détecteur de Boucles</div>
            <div id="loop-stats">
                <div>⏱️ Temps: <span id="elapsed-time">0</span>s</div>
                <div>🔄 setTimeout: <span id="timeout-count">0</span></div>
                <div>⚡ setInterval: <span id="interval-count">0</span></div>
                <div>🔁 Récursions: <span id="recursion-count">0</span></div>
            </div>
            <button onclick="window.LoopDetector.emergencyStop()" style="
                background: #dc3545; color: white; border: none; 
                padding: 3px 6px; border-radius: 3px; cursor: pointer;
                font-size: 10px; margin-top: 5px; width: 100%;
            ">🛑 ARRÊT D'URGENCE</button>
        `;
        
        document.body.appendChild(dashboard);
        
        // Masquer après 20 secondes si tout va bien
        setTimeout(() => {
            if (this.timeoutCount < 20 && this.intervalCount < 3) {
                dashboard.style.opacity = '0.3';
                dashboard.style.transform = 'scale(0.8)';
            }
        }, 20000);
    }
    
    /**
     * Mettre à jour le tableau de bord
     */
    updateDashboard() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const recursionTotal = Object.values(this.recursionCount).reduce((a, b) => a + b, 0);
        
        const elapsedEl = document.getElementById('elapsed-time');
        const timeoutEl = document.getElementById('timeout-count');
        const intervalEl = document.getElementById('interval-count');
        const recursionEl = document.getElementById('recursion-count');
        
        if (elapsedEl) elapsedEl.textContent = elapsed;
        if (timeoutEl) timeoutEl.textContent = this.timeoutCount;
        if (intervalEl) intervalEl.textContent = this.intervalCount;
        if (recursionEl) recursionEl.textContent = recursionTotal;
        
        // Changer les couleurs selon les seuils
        const dashboard = document.getElementById('loop-detector-dashboard');
        if (dashboard) {
            if (this.timeoutCount > 100 || this.intervalCount > 5 || recursionTotal > 20) {
                dashboard.style.background = 'rgba(220,53,69,0.9)'; // Rouge
            } else if (this.timeoutCount > 50 || recursionTotal > 10) {
                dashboard.style.background = 'rgba(255,193,7,0.9)'; // Orange
            } else {
                dashboard.style.background = 'rgba(0,0,0,0.8)'; // Normal
            }
        }
    }
    
    /**
     * Afficher une alerte
     */
    showAlert(title, message) {
        const alert = document.createElement('div');
        alert.style.cssText = `
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 999999;
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            font-weight: bold;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            animation: slideDown 0.3s ease;
        `;
        
        alert.innerHTML = `
            <div style="font-size: 14px;">🚨 ${title}</div>
            <div style="font-size: 12px; margin-top: 5px;">${message}</div>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

/**
 * Initialisation automatique
 */
(function() {
    console.log('🔍 Initialisation du détecteur de boucles...');
    
    // Attendre que le DOM soit prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.LoopDetector = new LoopDetector();
        });
    } else {
        window.LoopDetector = new LoopDetector();
    }
    
    console.log('✅ Détecteur de boucles prêt');
})();
