/**
 * GRADIS - Script de Nettoyage
 * Nettoie les anciens scripts et optimisations qui causent des conflits
 * Version: 1.0 - Nettoyage et stabilisation
 */

console.log('🧹 GRADIS - Script de nettoyage chargé');

/**
 * Nettoyer les anciens timers et processus
 */
function cleanupOldProcesses() {
    console.log('🧹 Nettoyage des anciens processus...');
    
    // Nettoyer les intervals existants (seulement les problématiques)
    let intervalId = setInterval(() => {}, 1);
    for (let i = Math.max(1, intervalId - 50); i <= intervalId; i++) {
        try {
            clearInterval(i);
        } catch (e) {
            // Ignorer les erreurs
        }
    }
    clearInterval(intervalId);
    
    // Nettoyer les timeouts existants (seulement les très longs)
    let timeoutId = setTimeout(() => {}, 1);
    for (let i = Math.max(1, timeoutId - 50); i <= timeoutId; i++) {
        try {
            clearTimeout(i);
        } catch (e) {
            // Ignorer les erreurs
        }
    }
    clearTimeout(timeoutId);
    
    console.log('✅ Anciens processus nettoyés');
}

/**
 * Restaurer les fonctions natives si elles ont été modifiées
 */
function restoreNativeFunctions() {
    console.log('🔧 Restauration des fonctions natives...');
    
    // Sauvegarder les fonctions natives si elles ne sont pas déjà sauvegardées
    if (!window._originalSetTimeout) {
        window._originalSetTimeout = window.setTimeout;
        window._originalSetInterval = window.setInterval;
        window._originalFetch = window.fetch;
    }
    
    // Restaurer temporairement pour éviter les conflits
    window.setTimeout = window._originalSetTimeout;
    window.setInterval = window._originalSetInterval;
    if (window._originalFetch) {
        window.fetch = window._originalFetch;
    }
    
    console.log('✅ Fonctions natives restaurées temporairement');
}

/**
 * Supprimer les éléments d'interface des anciens scripts
 */
function cleanupOldUI() {
    console.log('🧹 Nettoyage de l\'ancienne interface...');
    
    // Supprimer les anciens panneaux de diagnostic
    const oldPanels = [
        'admin-diagnostic-panel',
        'performance-widget',
        'emergency-stop-button'
    ];
    
    oldPanels.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.remove();
            console.log(`✅ Ancien panneau supprimé: ${id}`);
        }
    });
    
    // Supprimer les anciens boutons d'optimisation
    const oldButtons = document.querySelectorAll('[onclick*="optimize"], [onclick*="emergency"]');
    oldButtons.forEach(button => {
        if (button.textContent.includes('Optimiser') || button.textContent.includes('Urgence')) {
            button.remove();
        }
    });
    
    console.log('✅ Ancienne interface nettoyée');
}

/**
 * Nettoyer les styles CSS ajoutés dynamiquement
 */
function cleanupDynamicStyles() {
    console.log('🧹 Nettoyage des styles dynamiques...');
    
    // Supprimer les styles ajoutés par les anciens scripts
    const dynamicStyles = document.querySelectorAll('style[data-source*="optimization"], style[data-source*="performance"]');
    dynamicStyles.forEach(style => {
        style.remove();
    });
    
    // Nettoyer les classes ajoutées dynamiquement
    const elementsWithOptimizationClasses = document.querySelectorAll('[class*="optimized"], [class*="performance"]');
    elementsWithOptimizationClasses.forEach(element => {
        element.classList.forEach(className => {
            if (className.includes('optimized') || className.includes('performance')) {
                element.classList.remove(className);
            }
        });
    });
    
    console.log('✅ Styles dynamiques nettoyés');
}

/**
 * Réinitialiser les gestionnaires d'événements problématiques
 */
function resetEventHandlers() {
    console.log('🔧 Réinitialisation des gestionnaires d\'événements...');
    
    // Supprimer les anciens gestionnaires d'erreurs
    window.onerror = null;
    
    // Nettoyer les gestionnaires d'événements sur window
    const eventsToClean = ['error', 'unhandledrejection', 'beforeunload'];
    eventsToClean.forEach(eventType => {
        const listeners = window.getEventListeners ? window.getEventListeners(window)[eventType] : [];
        if (listeners) {
            listeners.forEach(listener => {
                window.removeEventListener(eventType, listener.listener);
            });
        }
    });
    
    console.log('✅ Gestionnaires d\'événements réinitialisés');
}

/**
 * Fonction principale de nettoyage
 */
function performCleanup() {
    console.log('🚀 Début du nettoyage complet...');
    
    try {
        // Étape 1: Nettoyer les anciens processus
        cleanupOldProcesses();
        
        // Étape 2: Restaurer les fonctions natives
        restoreNativeFunctions();
        
        // Étape 3: Nettoyer l'interface
        cleanupOldUI();
        
        // Étape 4: Nettoyer les styles
        cleanupDynamicStyles();
        
        // Étape 5: Réinitialiser les gestionnaires
        resetEventHandlers();
        
        console.log('✅ Nettoyage complet terminé avec succès');
        
        // Notification de succès
        if (window.StableUtils && window.StableUtils.showNotification) {
            window.StableUtils.showNotification('Interface nettoyée et stabilisée', 'success');
        }
        
    } catch (error) {
        console.error('❌ Erreur lors du nettoyage:', error);
    }
}

/**
 * Créer un bouton de nettoyage manuel
 */
function createCleanupButton() {
    const button = document.createElement('button');
    button.innerHTML = '🧹 Nettoyer';
    button.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 9999;
        background: #dc3545;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    
    button.addEventListener('click', function() {
        performCleanup();
        this.innerHTML = '✅ Nettoyé';
        this.style.background = '#28a745';
        
        setTimeout(() => {
            this.style.opacity = '0.5';
        }, 2000);
    });
    
    document.body.appendChild(button);
    
    // Masquer le bouton après 30 secondes
    setTimeout(() => {
        if (button.parentNode) {
            button.style.transition = 'opacity 1s ease';
            button.style.opacity = '0.3';
        }
    }, 30000);
}

/**
 * Initialisation automatique
 */
function initCleanup() {
    console.log('🚀 Initialisation du nettoyage...');
    
    // Effectuer le nettoyage après un court délai
    setTimeout(() => {
        performCleanup();
        createCleanupButton();
    }, 1000);
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', initCleanup);

// Initialisation immédiate si le DOM est déjà prêt
if (document.readyState !== 'loading') {
    initCleanup();
}

// Export global
window.CleanupUtils = {
    cleanup: performCleanup,
    cleanupProcesses: cleanupOldProcesses,
    restoreNatives: restoreNativeFunctions
};

console.log('🧹 Script de nettoyage prêt');
